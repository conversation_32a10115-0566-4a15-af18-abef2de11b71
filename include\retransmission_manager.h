#ifndef RETRANSMISSION_MANAGER_H
#define RETRANSMISSION_MANAGER_H

#include "Arduino.h"
#include "config.h"

// 重传机制核心常量定义
#define RETRANS_TIMEOUT_MS 2000          // 重传超时时间500ms
#define RETRANS_MAX_ATTEMPTS 2          // 最大重传次数2次
#define RETRANS_MAX_ACTIVE_ENTRIES 5    // 最大同时活跃重传条目数

// 重传条目结构
struct RetransmissionEntry {
    uint8_t responseCode;      // 要发送的响应码
    uint32_t lastSendTime;     // 最后发送时间
    uint8_t attemptCount;      // 重传次数
    bool active;               // 是否活跃
    
    RetransmissionEntry() : responseCode(0), lastSendTime(0), attemptCount(0), active(false) {}
};

/**
 * 重传管理器类
 * 管理蓝牙响应的重传机制，当发送响应后在500ms内没有收到新数据时自动重传
 */
class RetransmissionManager {
private:
    RetransmissionEntry entries[RETRANS_MAX_ACTIVE_ENTRIES];
    int activeEntryIndex;      // 当前活跃条目索引(-1表示无活跃条目)
    
    void sendResponseCode(uint8_t code);
    int findFreeEntry();
    void clearEntry(int index);
    
public:
    RetransmissionManager();
    
    /**
     * 发送响应并开始重传管理
     * @param responseCode 要发送的响应码(如BT_NEXT_FRAME)
     */
    void sendWithRetransmission(uint8_t responseCode);
    
    /**
     * 接收到手机新数据时调用，取消等待中的重传
     */
    void onDataReceived();
    
    /**
     * 检查超时并执行重传或中断传输
     * 需要在主循环中定期调用
     */
    void checkTimeouts();
    
    /**
     * 手动中断当前传输
     */
    void abortTransmission();
    
    /**
     * 获取当前活跃重传条目数量
     */
    int getActiveEntryCount() const;
    
    /**
     * 重置所有重传状态
     */
    void reset();
};

#endif // RETRANSMISSION_MANAGER_H