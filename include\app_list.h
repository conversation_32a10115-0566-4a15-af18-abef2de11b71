#ifndef APP_LIST_H
#define APP_LIST_H

#include "config.h"
#include "bluetooth_protocol.h"
#include "debug_utils.h"
#include <stddef.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "file_handler.h"
#include "gif_player.h"

extern char **file_list;

void initFileList();
void freeFileList();
void addFileToList(const char *filename);
void printFileList();
int processFileList();
bool play_list_GIF();
void updateGIF_FOR_list();

enum  list_mode{
    LIST_NO_MODE=0,
    LIST_GIF_MODE,
    LIST_TEXT_MODE,
    LIST_UNKNOW
};

#endif // __LIST_H