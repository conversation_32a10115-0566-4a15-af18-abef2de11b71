# 文本相关设置蓝牙帧格式说明

 0x00  // 设置文本显示方向命令（默认：正向显示）

 0x01  // 设置文本竖向显示命令

 0x02 // 设置16x16字体命令（默认）

 0x03 // 设置32x32字体命令

以上文本固定格式： AA 55 命令 00 00 0D 0A

## 文本设置命令 (0x04)

### 命令格式
```
AA 55 04  [数据长度高字节] [数据长度低字节] [屏幕区域] [字体数据...] 0D 0A
```

### 参数说明
- 命令码：0x04
- 屏幕区域：选择显示区域
- 数据长度：2字节，表示字体数据的字节数
- 字体数据：16x16或32x32字体点阵数据

## 参数详细说明

### 屏幕区域
| 值 | 区域 | 说明 |
|---|------|------|
| 0x01 | 上半屏 | 仅在上半屏显示文本(16x16) |
| 0x02 | 下半屏 | 仅在下半屏显示文本(16x16) |
| 0x03 | 全屏 | 全屏显示文本(32x32) |

### 字体数据格式

#### 16x16字体数据
- 每个字符：32字节 (16个uint16_t)
- 数据格式：每行2字节，共16行
- 字节序：高字节在前，低字节在后
- 数据长度：字符数×32字节

#### 32x32字体数据
- 每个字符：128字节 (64个uint16_t)
- 数据格式：每行4字节，共32行
- 字节序：高字节在前，低字节在后
- 数据长度：字符数×128字节

## 使用示例

### 16x16字体单字符显示
```
// 上半屏显示一个16x16字符
AA 55 04  00 21 01[32字节字体数据] 0D 0A
// 数据长度：33 = 0x21
```

### 16x16字体多字符显示
```
// 上半屏显示两个16x16字符
AA 55 04  00 41 01 [字符1:32字节] [字符2:32字节] 0D 0A
// 数据长度：32×2 + 1= 65 = 0x41
```

### 32x32字体显示
```
// 全屏显示一个32x32字符
AA 55 04  00 81 03 [128字节字体数据] 0D 0A
// 数据长度：128+1 = 0x81

// 全屏显示两个32x32字符
AA 55 04  01 00 03 [字符1:128字节] [字符2:128字节] 0D 0A
// 数据长度：128×2+ 1 = 256 + 1 = 0x0101
```

## 注意事项
1. 数据长度为2字节，高字节在前，低字节在后
2. 数据长度包含屏幕区域
3. 最大帧大小限制为8192字节
4. 16x16字体最多可传输约255个字符
5. 32x32字体最多可传输约63个字符
6. 屏幕区域设置会影响文本显示位置
7. 文本设置会立即更新显示内容