# 蓝牙GIF播放帧格式说明

## 概述

本文档定义了通过蓝牙协议控制LED显示屏播放GIF动画的数据帧格式。该协议支持播放存储在设备文件系统中的GIF文件，并可与分区显示模式配合使用。

## 1. 协议基本格式

### 1.1 帧结构
```
帧头    命令编号    数据长度    数据内容    帧尾
AA 55   15         XX XX       [data]     0D 0A
```

### 1.2 字段说明
- **帧头**: 固定为 `AA 55`
- **命令编号**: `15` (0x15) - GIF播放命令
- **数据长度**: 两字节，表示数据内容的字节数（大端序）
- **数据内容**: GIF文件名（ASCII字符串，不包含路径）
- **帧尾**: 固定为 `0D 0A`

## 2. GIF播放命令 (0x15)

### 2.1 命令格式
```
包头：AA 55
命令编号：15
数据长度：00 XX（XX为文件名长度）
数据内容：[GIF文件名]
包尾：0D 0A
```

### 2.2 数据内容格式
- **文件名**: UTF-8编码字符串，仅包含文件名（如 "test.gif"），不包含路径
- **长度**: 1-64字节（FILE_MAX_NAME_LENGTH = 64）
- **编码**: UTF-8
- **扩展名**: 必须为 ".gif"（不区分大小写）

### 2.3 示例数据包

#### 示例1：播放 "demo.gif"
```
AA 55 15 00 08 64 65 6D 6F 2E 67 69 66 0D 0A
```
解析：
- 帧头: `AA 55`
- 命令: `15` (GIF播放命令)
- 长度: `00 08` (8字节)
- 数据: `64 65 6D 6F 2E 67 69 66` ("demo.gif" 的UTF-8编码)
- 帧尾: `0D 0A`

#### 示例2：播放 "animation.gif"
```
AA 55 15 00 0C 61 6E 69 6D 61 74 69 6F 6E 2E 67 69 66 0D 0A
```
解析：
- 帧头: `AA 55`
- 命令: `15` (GIF播放命令)
- 长度: `00 0C` (12字节)
- 数据: `61 6E 69 6D 61 74 69 6F 6E 2E 67 69 66` ("animation.gif" 的UTF-8编码)
- 帧尾: `0D 0A`

## 3. 显示模式

### 3.1 全屏模式（非分区模式）
- GIF会居中显示在整个屏幕上
- 偏移量自动计算：`(屏幕宽度 - GIF宽度) / 2`
- 停止当前播放的任何GIF
- 系统状态切换到 `loop_state_gif`

### 3.2 分区模式
- GIF显示在指定的分区位置（左侧或右侧）
- 尺寸限制为32x32像素
- 与文字内容同时显示
- 系统状态保持 `loop_state_split_display`
- 根据 `currentSplitMode` 确定显示位置：
  - `SPLIT_MODE_GIF_LEFT_TEXT32/16`: GIF显示在左侧(X=0)
  - `SPLIT_MODE_GIF_RIGHT_TEXT32/16`: GIF显示在右侧(X=32)

## 4. 技术实现

### 4.1 文件路径处理
- 输入文件名: `filename` (如 "test.gif")
- 完整路径: `/gifs/filename` (如 "/gifs/test.gif")
- 存储路径常量: `GIF_STORAGE_PATH` = "/gifs/"

### 4.2 播放模式选择
系统自动选择最适合的播放模式：
- 小文件（<100KB）: 内存模式 - `playGIF()`
- 大文件（≥100KB）: 流式模式 - `playGIFStream()`
- 统一入口：`playGIFAuto()` 智能选择

### 4.3 内存管理
- 支持PSRAM和堆内存
- 自动垃圾回收和内存碎片整理
- 播放前停止当前GIF并释放内存

## 5. 响应机制

### 5.1 成功响应
播放成功时返回：
```
响应命令: 0x15
响应内容: "OK:Playing GIF"
```

### 5.2 错误响应

#### 文件名为空
```
响应命令: 0x15
响应内容: "ERROR:Empty filename"
```

#### 文件不存在
```
响应命令: 0x15
响应内容: "ERROR:File not found"
```

#### 播放失败
```
响应命令: 0x15
响应内容: "ERROR:Play failed"
```

## 6. 约束条件

### 6.1 文件要求
- 文件格式：GIF动画
- 最大尺寸：5MB (FILE_MAX_SIZE)
- 推荐分辨率：64x32 (全屏) 或 32x32 (分区模式)
- 存储位置：设备内部文件系统 "/gifs/" 目录

### 6.2 性能限制
- 同时只能播放一个GIF
- 新的播放命令会停止当前播放
- 内存不足时自动降级到流式播放
- 帧率受系统性能和文件大小影响

### 6.3 兼容性
- 支持标准GIF89a格式
- 支持透明度和动画
- 支持循环播放
- 兼容各种调色板模式

## 7. 调试信息

### 7.1 成功日志示例
```
Starting GIF stream playback: /gifs/test.gif
Successfully opened GIF stream; Canvas size = 32 x 32
Display offset: x=16, y=0
Free heap after GIF stream open: 234567 bytes
Started playing GIF: test.gif
```

### 7.2 错误日志示例
```
File not found: nonexistent.gif
GIF file not found: /gifs/nonexistent.gif
Failed to play GIF: nonexistent.gif
```

## 8. 文件传输协议

由于GIF文件通常较大，系统支持分批传输文件到设备。文件传输协议包含以下命令：

### 8.1 文件传输开始命令 (0x10)

#### 8.1.1 命令格式
```
包头：AA 55
命令编号：10
数据长度：00 XX（XX为文件名长度+4）
数据内容：[文件大小4字节][文件名]
包尾：0D 0A
```

#### 8.1.2 数据内容格式
- **文件大小**: 4字节，小端序（LSB在前）
- **文件名**: UTF-8编码字符串，不包含路径
- **总长度**: 文件名长度 + 4字节

#### 8.1.3 示例数据包
传输文件 "demo.gif"，大小为 10240 字节：
```
AA 55 10 00 0C 00 28 00 00 64 65 6D 6F 2E 67 69 66 0D 0A
```
解析：
- 帧头: `AA 55`
- 命令: `10` (文件传输开始)
- 长度: `00 0C` (12字节 = 4字节文件大小 + 8字节文件名)
- 文件大小: `00 28 00 00` (10240，小端序)
- 文件名: `64 65 6D 6F 2E 67 69 66` ("demo.gif")
- 帧尾: `0D 0A`

#### 8.1.4 响应机制
成功时返回：`0xC0` (BT_NEXT_FRAME)
失败时返回错误响应

### 8.2 文件数据传输命令 (0x11)

#### 8.2.1 命令格式
```
包头：AA 55
命令编号：11
数据长度：XX XX（数据块长度）
数据内容：[文件数据块]
包尾：0D 0A
```

#### 8.2.2 数据传输特性
- **数据块大小**: 最大2048字节（FILE_TRANSFER_CHUNK_SIZE）
- **传输模式**: 双缓冲区异步写入
- **流控制**: 每包发送后等待 `0xC0` 响应
- **超时检测**: 每500ms检查包丢失

#### 8.2.3 示例数据包
```
AA 55 11 08 00 [2048字节GIF数据] 0D 0A
```
解析：
- 帧头: `AA 55`
- 命令: `11` (文件数据传输)
- 长度: `08 00` (2048字节)
- 数据: GIF文件的二进制数据块
- 帧尾: `0D 0A`

#### 8.2.4 响应机制
每收到一个数据包后：
- 成功：返回 `0xC0` (请求下一包)
- 失败：返回错误响应并终止传输

### 8.3 文件传输结束命令 (0x12)

#### 8.3.1 命令格式
```
包头：AA 55
命令编号：12
数据长度：00 00
数据内容：[无]
包尾：0D 0A
```

#### 8.3.2 示例数据包
```
AA 55 12 00 00 0D 0A
```

#### 8.3.3 验证机制
- 检查实际接收大小与预期大小是否匹配
- 完成最终数据写入和文件关闭
- 计算传输统计信息（时间、速度）

#### 8.3.4 响应内容
成功：
```
响应命令: 0x12
响应内容: "OK:File saved successfully"
```
失败：
```
响应命令: 0x12
响应内容: "ERROR:Size mismatch"
```

### 8.4 文件列表查询命令 (0x13)

#### 8.4.1 命令格式
```
包头：AA 55
命令编号：13
数据长度：00 00
数据内容：[无]
包尾：0D 0A
```

#### 8.4.2 响应格式
```
响应命令: 0x13
响应内容: "文件名1:大小1;文件名2:大小2;..."
```

#### 8.4.3 示例响应
```
"demo.gif:10240;animation.gif:25600;test.gif:5120"
```

### 8.5 文件删除命令 (0x14)

#### 8.5.1 命令格式
```
包头：AA 55
命令编号：14
数据长度：00 XX（XX为文件名长度）
数据内容：[文件名]
包尾：0D 0A
```

#### 8.5.2 示例数据包
删除 "demo.gif"：
```
AA 55 14 00 08 64 65 6D 6F 2E 67 69 66 0D 0A
```

#### 8.5.3 响应内容
成功：
```
响应命令: 0x14
响应内容: "OK:File deleted"
```
失败：
```
响应命令: 0x14
响应内容: "ERROR:File not found"
```

## 9. 文件传输流程

### 9.1 完整传输流程
```mermaid
sequenceDiagram
    participant C as 客户端
    participant D as 设备
    
    C->>D: 0x10 文件传输开始(文件大小+文件名)
    D-->>C: 0xC0 准备接收
    
    loop 数据传输
        C->>D: 0x11 文件数据块
        D-->>C: 0xC0 请求下一包
    end
    
    C->>D: 0x12 文件传输结束
    D-->>C: OK/ERROR 传输结果
    
    Note over D: 文件保存到 /gifs/ 目录
    
    C->>D: 0x15 播放GIF(文件名)
    D-->>C: OK/ERROR 播放结果
```

### 9.2 错误处理机制

#### 9.2.1 超时处理
- 传输超时：2000ms（FILE_TRANSFER_TIMEOUT）
- 包丢失检测：500ms间隔
- 自动重传：等待客户端重发

#### 9.2.2 错误恢复
- 传输中断：自动清理临时文件
- 内存不足：切换到流式处理模式
- 文件系统错误：返回具体错误信息

#### 9.2.3 约束检查
- 文件大小限制：5MB（FILE_MAX_SIZE）
- 文件名长度：64字节（FILE_MAX_NAME_LENGTH）
- 数据块大小：2048字节（FILE_TRANSFER_CHUNK_SIZE）
- 同时传输：仅支持一个文件传输

## 10. 使用建议

### 10.1 最佳实践
1. 使用32x32分辨率的GIF以获得最佳兼容性
2. 优化GIF文件大小以提高播放流畅度
3. 使用有意义的文件名便于管理
4. 传输前先查询文件列表避免重复传输
5. 传输大文件时建议分多次传输
6. 传输完成后验证文件完整性

### 10.2 性能优化
1. 使用最大数据块大小（2048字节）提高传输效率
2. 避免在GIF播放期间进行文件传输
3. 合理设置超时时间适应网络环境
4. 监控设备内存使用情况

### 10.3 故障排除
1. 检查文件是否存在于 "/gifs/" 目录
2. 验证文件名拼写和扩展名
3. 确认文件格式为有效的GIF
4. 检查传输过程中的错误响应
5. 监控设备存储空间是否充足
6. 验证文件大小是否超出限制

---

**注意**: 此协议设计用于LED显示屏控制系统。建议的使用流程：
1. 先传输GIF文件到设备（0x10→0x11→0x12）
2. 设置分区模式（如需要）
3. 发送GIF播放命令（0x15）
4. 发送文本命令实现gif背景+文本前景功能