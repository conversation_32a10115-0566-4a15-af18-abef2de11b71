# 蓝牙计分模式帧格式说明

## 📋 概述

本文档描述计分模式的蓝牙帧格式规范。计分模式支持四个显示区域的内容设置，包括文本显示和数值计分功能。系统提供两种数据类型：

1. **文本数据**: 用于在文本区域显示自定义文字内容，支持16×16点阵字体
2. **数值数据**: 用于在计分区域显示和操作数字，支持加减运算

## 🔧 命令定义

- **命令代码**: `0x21` (`BT_CMD_SCORE_MODE`)
- **功能**: 设置计分模式的显示内容和参数
- **数据类型**: 根据区域选择自动识别文本或数值数据

## 📦 帧格式

> **⚠️ 重要说明**: 数据长度字段采用双字节格式（高字节+低字节），这与协议解析器的实现保持一致。计分模式根据数据长度和区域类型自动识别数据格式。

### 计分模式帧结构 (0x21)

```
| 帧头1 | 帧头2 | 命令 | 数据长度高字节 | 数据长度低字节 | 数据区域 | 帧尾1 | 帧尾2 |
|-------|-------|------|--------------|--------------|---------| ------|-------|
| 0xAA  | 0x55  | 0x21 |   高字节     |   低字节     | 变长数据 | 0x0D  | 0x0A  |
```

## 🎯 区域定义

计分模式支持四个独立的显示区域：

| 区域代码 | 宏定义                      | 区域名称 | 数据类型 | 说明 |
| -------- | --------------------------- | -------- | -------- | ---- |
| 0x00     | `BT_SCORE_REGION_LEFT_TEXT` | 左上文本 | 文本数据 | 显示自定义文字 |
| 0x01     | `BT_SCORE_REGION_LEFT_SCORE`| 左下计分 | 数值数据 | 显示和操作分数 |
| 0x02     | `BT_SCORE_REGION_RIGHT_TEXT`| 右上文本 | 文本数据 | 显示自定义文字 |
| 0x03     | `BT_SCORE_REGION_RIGHT_SCORE`| 右下计分| 数值数据 | 显示和操作分数 |

## 📝 文本数据协议

### 适用区域
- 左上文本区域 (0x00)
- 右上文本区域 (0x02)

### 帧结构
```
| 帧头1 | 帧头2 | 命令 | 数据长度高字节 | 数据长度低字节 | 数据区域 | 帧尾1 | 帧尾2 |
|-------|-------|------|--------------|--------------|---------| ------|-------|
| 0xAA  | 0x55  | 0x21 |   高字节     |   低字节     |36-324字节| 0x0D  | 0x0A  |
```

### 文本数据区域结构

```
| 字节位置 | 字段名称 | 数据类型 | 取值范围 | 说明 |
|----------|----------|----------|----------|------|
| 0        | 区域选择 | uint8_t  | 0x00,0x02 | 文本区域代码 |
| 1        | 红色分量 | uint8_t  | 0-255    | 文字颜色R分量 |
| 2        | 绿色分量 | uint8_t  | 0-255    | 文字颜色G分量 |
| 3        | 蓝色分量 | uint8_t  | 0-255    | 文字颜色B分量 |
| 4-N      | 点阵数据 | uint8_t[] | 32-320字节 | 16×16字体点阵数据 |
```

### 文本数据长度规范

- **最小长度**: 36字节 (`BT_SCORE_TEXT_DATA_LEN_MIN`)
  - 区域选择: 1字节
  - RGB颜色: 3字节  
  - 点阵数据: 32字节 (1个字符)

- **最大长度**: 324字节 (`BT_SCORE_TEXT_DATA_LEN_MAX`)
  - 区域选择: 1字节
  - RGB颜色: 3字节
  - 点阵数据: 320字节 (10个字符)

- **字符规格**: 每个16×16字符占用32字节 (`BT_SCORE_CHAR_BYTES`)
- **最大字符数**: 10个字符 (`BT_SCORE_MAX_CHARS`)

## 🔢 数值数据协议

### 适用区域
- 左下计分区域 (0x01)
- 右下计分区域 (0x03)

### 帧结构
```
| 帧头1 | 帧头2 | 命令 | 数据长度高字节 | 数据长度低字节 | 数据区域 | 帧尾1 | 帧尾2 |
|-------|-------|------|--------------|--------------|---------| ------|-------|
| 0xAA  | 0x55  | 0x21 |     0x00     |     0x09     | 9字节数据| 0x0D  | 0x0A  |
```

### 数值数据区域结构 (9 字节)

```
| 字节位置 | 字段名称 | 数据类型 | 取值范围 | 说明 |
|----------|----------|----------|----------|------|
| 0        | 区域选择 | uint8_t  | 0x01,0x03 | 计分区域代码 |
| 1        | 红色分量 | uint8_t  | 0-255    | 数字颜色R分量 |
| 2        | 绿色分量 | uint8_t  | 0-255    | 数字颜色G分量 |
| 3        | 蓝色分量 | uint8_t  | 0-255    | 数字颜色B分量 |
| 4        | 初值高字节| uint8_t  | 0-255    | 初始数值高8位 |
| 5        | 初值低字节| uint8_t  | 0-255    | 初始数值低8位 |
| 6        | 操作类型 | uint8_t  | 0x00-0x01 | 加减运算选择 |
| 7        | 操作值高字节| uint8_t | 0-255    | 运算数值高8位 |
| 8        | 操作值低字节| uint8_t | 0-255    | 运算数值低8位 |
```

### 数值范围和操作

- **数值范围**: 0-999 (使用16位存储，大端序)
- **初始值**: 设置计分区域的起始数值
- **操作类型**:
  - 0x00: 加法运算
  - 0x01: 减法运算
- **操作值**: 与初始值进行运算的数值
- **字节序**: 大端序 (高字节在前，低字节在后)

## 使用示例

### 文本数据示例

#### 左上区域显示红色文字
```
// 左上区域，红色文字，显示1个字符
AA 55 21 00 24 00 FF 00 00 [32字节点阵数据] 0D 0A
// 数据长度: 36字节 = 0x24
```

#### 右上区域显示蓝色文字
```
// 右上区域，蓝色文字，显示2个字符  
AA 55 21 00 44 02 00 00 FF [64字节点阵数据] 0D 0A
// 数据长度: 68字节 = 0x44
```

### 数值数据示例

#### 左下计分区域设置
```
// 左下计分，绿色数字，初值100，加10
AA 55 21 00 09 01 00 FF 00 00 64 00 00 0A 0D 0A
// 区域:0x01, 颜色:绿色, 初值:100, 操作:加, 数值:10
```

#### 右下计分区域设置
```
// 右下计分，黄色数字，初值50，减5
AA 55 21 00 09 03 FF FF 00 00 32 01 00 05 0D 0A
// 区域:0x03, 颜色:黄色, 初值:50, 操作:减, 数值:5
```

### 常用颜色RGB值
| 颜色 | R | G | B | 十六进制 |
|------|---|---|---|----------|
| 白色 | 255 | 255 | 255 | FF FF FF |
| 红色 | 255 | 0 | 0 | FF 00 00 |
| 绿色 | 0 | 255 | 0 | 00 FF 00 |
| 蓝色 | 0 | 0 | 255 | 00 00 FF |
| 黄色 | 255 | 255 | 0 | FF FF 00 |
| 紫色 | 255 | 0 | 255 | FF 00 FF |
| 青色 | 0 | 255 | 255 | 00 FF FF |
| 黑色 | 0 | 0 | 0 | 00 00 00 |

## 注意事项

1. **区域识别**: 系统根据区域代码自动识别数据类型
   - 0x00, 0x02: 文本数据协议
   - 0x01, 0x03: 数值数据协议

2. **数据长度验证**: 
   - 文本数据: 36-324字节
   - 数值数据: 固定9字节

3. **字符限制**: 文本区域最多显示10个16×16字符

4. **数值限制**: 计分数值范围0-999

5. **颜色设置**: 每个区域可独立设置RGB颜色

6. **字节序**: 数值数据采用大端序存储

7. **即时更新**: 设置立即生效，触发对应区域显示更新

8. **点阵格式**: 文本数据使用16×16点阵，每字符32字节