# 蓝牙边框帧格式说明文档

## 基本帧结构

```
[帧头1][帧头2][命令][数据长度][数据内容][帧尾1][帧尾2]
```

- 帧头：0xAA 0x55
- 帧尾：0x0D 0x0A
- 数据长度：2 字节，表示数据内容的字节数

## 边框设置命令 (0x09)

### 命令格式

```
AA 55 09 00 04 [边框样式][颜色索引][流动效果][流动速度] 0D 0A
```

### 参数说明

- 命令码：0x09
- 数据长度：4 字节
- 边框样式：见边框样式对照表
- 颜色索引：见颜色索引对照表
- 流动效果：见流动效果对照表
- 流动速度：1-10 (1=最慢，10=最快)

### 边框样式对照表

| 值   | 样式名称 | 说明                 |
| ---- | -------- | -------------------- |
| 0x00 | 无边框   | 清除边框显示         |
| 0x01 | 实线边框 | 连续的边框线条       |
| 0x02 | 点线边框 | 虚线样式的边框       |
| 0x03 | 角落边框 | 仅在四个角落显示边框 |
| 0x04 | 彩虹边框 | 多彩渐变色边框       |

### 颜色索引对照表

| 值   | 颜色名称 | RGB 值 | 说明   |
| ---- | -------- | ------ | ------ |
| 0x00 | 红色     | 0xF800 | 纯红色 |
| 0x01 | 绿色     | 0x07E0 | 纯绿色 |
| 0x02 | 蓝色     | 0x001F | 纯蓝色 |
| 0x03 | 黄色     | 0xFFE0 | 纯黄色 |
| 0x04 | 紫色     | 0xF81F | 洋红色 |
| 0x05 | 青色     | 0x07FF | 青蓝色 |
| 0x06 | 白色     | 0xFFFF | 纯白色 |

### 流动效果对照表

| 值   | 效果名称   | 说明               |
| ---- | ---------- | ------------------ |
| 0x00 | 静止显示   | 边框固定不动       |
| 0x01 | 顺时针流动 | 边框效果顺时针移动 |
| 0x02 | 逆时针流动 | 边框效果逆时针移动 |
| 0x03 | 闪烁效果   | 边框闪烁显示       |

### 示例命令

```
AA 55 09 00 04 01 00 00 05 0D 0A  // 红色实线边框，静止显示，速度5
AA 55 09 00 04 02 01 01 07 0D 0A  // 绿色点线边框，顺时针流动，速度7
AA 55 09 00 04 03 06 03 04 0D 0A  // 白色角落边框，闪烁效果，速度4
AA 55 09 00 04 04 00 02 09 0D 0A  // 彩虹边框，逆时针流动，速度9
AA 55 09 00 04 00 00 00 00 0D 0A  // 清除边框（后续参数被忽略）
```

## 特殊逻辑说明

### 无边框模式

- 当边框样式设置为 0x00 时，系统将清除所有边框效果
- 此时颜色索引、流动效果、流动速度参数将被忽略
- 边框清除后立即生效

### 彩虹边框模式

- 当边框样式设置为 0x04 时，系统使用内置彩虹色渐变
- 此时颜色索引参数将被忽略（可设置为任意值）
- 彩虹色会根据位置自动计算，无需手动指定颜色

### 角落边框限制

- 当边框样式设置为 0x03 时，流动效果仅支持静止(0x00)和闪烁(0x03)
- 如果设置了顺时针(0x01)或逆时针(0x02)流动，系统会自动转为静止显示
- 速度参数仍然有效，用于控制闪烁频率

### 绘制顺序

边框的绘制顺序为：背景 → 文本 → 边框

- 边框始终显示在最上层，不会被文本或背景遮挡
- 边框不会影响文本的显示位置和大小

## 错误处理

### 参数验证

系统会对所有参数进行有效性检查：

- 边框样式：0-4，超出范围则不执行命令
- 颜色索引：0-6，超出范围则不执行命令
- 流动效果：0-3，超出范围则不执行命令
- 流动速度：1-10，超出范围则不执行命令

### 错误反馈

当参数无效时，串口监视器会输出相应的错误信息：

```
错误: 无效的边框样式 5 (有效范围: 0-4)
错误: 无效的颜色索引 7 (有效范围: 0-6)
错误: 无效的边框效果 4 (有效范围: 0-3)
错误: 无效的边框速度 11 (有效范围: 1-10)
```

## 注意事项

1. 所有数值均为十六进制
2. 边框效果与文本特效独立运行，互不影响
3. 设置新边框会立即替换当前边框效果
4. 边框颜色使用 RGB565 格式存储
5. 流动速度建议范围：1-10，过快可能导致视觉疲劳
6. 彩虹边框的颜色渐变算法内置，无法自定义颜色序列
