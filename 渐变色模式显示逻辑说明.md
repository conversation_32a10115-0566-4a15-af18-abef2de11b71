# 渐变色模式显示逻辑说明

本文档详细说明了 LED 矩阵屏显示系统中三种渐变色模式（上下渐变、左右渐变、45 度斜渐变）的实现原理和显示逻辑。

## 目录

- [概述](#概述)
- [渐变色组合定义](#渐变色组合定义)
- [核心渐变色计算函数](#核心渐变色计算函数)
- [三种渐变模式详解](#三种渐变模式详解)
  - [1. 上下渐变模式](#1-上下渐变模式)
  - [2. 左右渐变模式](#2-左右渐变模式)
  - [3. 45 度斜渐变模式](#3-45度斜渐变模式)
- [渐变色应用函数](#渐变色应用函数)
- [技术实现细节](#技术实现细节)

## 概述

渐变色系统支持 9 种预定义的颜色组合，分为三大类：

- **上下渐变**：3 种组合（BT_GRADIENT_VERTICAL_1~3）
- **左右渐变**：3 种组合（BT_GRADIENT_HORIZONTAL_1~3）
- **45 度斜渐变**：3 种组合（BT_GRADIENT_DIAGONAL_45_1~3）

每种渐变都使用 7 种颜色进行平滑过渡，颜色索引从 0 到 6。

## 渐变色组合定义

```cpp
// 渐变色组合结构
struct GradientColors {
    uint8_t colors[7][3];  // 7种颜色，每种颜色3个分量(R,G,B)
};

// 预定义的9种渐变色组合
const GradientColors gradientCombinations[9] = {
    // 上下渐变组合1：红橙黄绿青蓝紫
    {{{255,0,0}, {255,127,0}, {255,255,0}, {0,255,0}, {0,255,255}, {0,0,255}, {127,0,255}}},

    // 上下渐变组合2：明亮彩虹色（红粉紫蓝青绿黄）
    {{{255,0,0}, {255,0,255}, {128,0,255}, {0,128,255}, {0,255,255}, {0,255,0}, {255,255,0}}},

    // 上下渐变组合3：紫蓝青绿黄橙红
    {{{127,0,255}, {0,0,255}, {0,255,255}, {0,255,0}, {255,255,0}, {255,127,0}, {255,0,0}}},

    // 左右渐变组合1：火焰渐变（红→橙→黄→白→黄→橙→红）
    {{{255,0,0}, {255,127,0}, {255,255,0}, {255,255,255}, {255,255,0}, {255,127,0}, {255,0,0}}},

    // 左右渐变组合2：明亮霓虹色（粉红→橙→黄→绿→青→蓝→紫）
    {{{255,20,147}, {255,165,0}, {255,255,0}, {0,255,0}, {0,255,255}, {0,0,255}, {138,43,226}}},

    // 左右渐变组合3：森林渐变（深绿→浅绿→黄绿→白→黄绿→浅绿→深绿）
    {{{0,100,0}, {0,200,0}, {127,255,0}, {255,255,255}, {127,255,0}, {0,200,0}, {0,100,0}}},

    // 45度渐变组合1：日落渐变（紫→蓝→青→绿→黄→橙→红）
    {{{138,43,226}, {0,0,255}, {0,255,255}, {0,255,0}, {255,255,0}, {255,165,0}, {255,0,0}}},

    // 45度渐变组合2：海洋渐变（深蓝→青→绿→白→绿→青→深蓝）
    {{{0,0,139}, {0,191,255}, {0,255,127}, {255,255,255}, {0,255,127}, {0,191,255}, {0,0,139}}},

    // 45度渐变组合3：霓虹渐变（粉→紫→蓝→青→绿→黄→橙）
    {{{255,20,147}, {138,43,226}, {0,0,255}, {0,255,255}, {0,255,0}, {255,255,0}, {255,165,0}}}
};
```

## 核心渐变色计算函数

系统的核心函数是 `getGradientColor(int x, int y, bool isUpper, uint8_t gradientMode)`，它根据像素位置和渐变模式计算出对应的颜色值。

```cpp
// 获取渐变色 - 完整实现代码
uint16_t getGradientColor(int x, int y, bool isUpper, uint8_t gradientMode)
{
    if (gradientMode == BT_GRADIENT_FIXED)
    {
        // 固定色，返回基础颜色
        return isUpper ? colorState.upperTextColor : colorState.lowerTextColor;
    }

    // 获取对应的渐变组合
    int combinationIndex = -1;
    bool isVertical = false;

    if (gradientMode >= BT_GRADIENT_VERTICAL_1 && gradientMode <= BT_GRADIENT_VERTICAL_3)
    {
        combinationIndex = gradientMode - BT_GRADIENT_VERTICAL_1; // 0,1,2
        isVertical = true;
    }
    else if (gradientMode >= BT_GRADIENT_HORIZONTAL_1 && gradientMode <= BT_GRADIENT_HORIZONTAL_3)
    {
        combinationIndex = gradientMode - BT_GRADIENT_HORIZONTAL_1 + 3; // 3,4,5
        isVertical = false;
    }
    else if (gradientMode >= BT_GRADIENT_DIAGONAL_45_1 && gradientMode <= BT_GRADIENT_DIAGONAL_45_3)
    {
        combinationIndex = gradientMode - BT_GRADIENT_DIAGONAL_45_1 + 6; // 6,7,8
        isVertical = false;                                              // 45度渐变使用特殊处理，不是垂直渐变
    }

    if (combinationIndex < 0 || combinationIndex >= 9)
    {
        // 无效的渐变模式，返回基础颜色
        return isUpper ? colorState.upperTextColor : colorState.lowerTextColor;
    }

    const GradientColors &gradient = gradientCombinations[combinationIndex];
    int colorIndex = 0;

    if (isVertical)
    {
        // 🔧 上下渐变：根据Y坐标计算颜色索引（使用动态屏幕高度）
        int relativeY = y;                          // 使用绝对Y坐标
        int effectiveHeight = currentScreen.height; // 使用动态屏幕高度
        if (!isUpper && currentScreen.height == 32)
        {
            // 16x16双屏模式下的下半屏
            relativeY = y - 16;
            effectiveHeight = 16;
        }
        colorIndex = (relativeY * 7) / effectiveHeight; // 动态高度分成7块
    }
    else if (gradientMode >= BT_GRADIENT_DIAGONAL_45_1 && gradientMode <= BT_GRADIENT_DIAGONAL_45_3)
    {
        // 🔧 45度渐变：根据对角线位置计算颜色索引（左上到右下）
        int diagonalPosition = x + y;
        int maxDiagonal = currentScreen.width + currentScreen.height - 2;
        colorIndex = (diagonalPosition * 7) / maxDiagonal; // 对角线距离分成7块
    }
    else
    {
        // 🔧 左右渐变：根据X坐标计算颜色索引（使用动态屏幕宽度）
        colorIndex = (x * 7) / currentScreen.width; // 动态屏幕宽度分成7块
    }

    // 确保索引在有效范围内
    if (colorIndex < 0)
        colorIndex = 0;
    if (colorIndex >= 7)
        colorIndex = 6;

    // 获取对应颜色并转换为RGB565
    uint8_t r = gradient.colors[colorIndex][0];
    uint8_t g = gradient.colors[colorIndex][1];
    uint8_t b = gradient.colors[colorIndex][2];

    return rgb888to565(r, g, b);
}
```

## 三种渐变模式详解

### 1. 上下渐变模式

**模式标识：** `BT_GRADIENT_VERTICAL_1`、`BT_GRADIENT_VERTICAL_2`、`BT_GRADIENT_VERTICAL_3`

**计算逻辑：**

```cpp
if (isVertical) {
    // 上下渐变：根据Y坐标计算颜色索引
    int relativeY = y;                          // 使用绝对Y坐标
    int effectiveHeight = currentScreen.height; // 使用动态屏幕高度

    if (!isUpper && currentScreen.height == 32) {
        // 16x16双屏模式下的下半屏
        relativeY = y - 16;
        effectiveHeight = 16;
    }

    colorIndex = (relativeY * 7) / effectiveHeight; // 动态高度分成7块
}
```

**显示效果：**

- 颜色从屏幕顶部到底部呈现垂直渐变
- 屏幕高度被均匀分成 7 个区域，每个区域对应一种颜色
- 支持双屏模式（16x16 上下分屏）的独立渐变

**颜色组合：**

- **组合 1**：红 → 橙 → 黄 → 绿 → 青 → 蓝 → 紫（经典彩虹色）
- **组合 2**：红 → 粉 → 紫 → 蓝 → 青 → 绿 → 黄（明亮彩虹色）
- **组合 3**：紫 → 蓝 → 青 → 绿 → 黄 → 橙 → 红（反向彩虹色）

### 2. 左右渐变模式

**模式标识：** `BT_GRADIENT_HORIZONTAL_1`、`BT_GRADIENT_HORIZONTAL_2`、`BT_GRADIENT_HORIZONTAL_3`

**计算逻辑：**

```cpp
else {
    // 左右渐变：根据X坐标计算颜色索引
    colorIndex = (x * 7) / currentScreen.width; // 动态屏幕宽度分成7块
}
```

**显示效果：**

- 颜色从屏幕左侧到右侧呈现水平渐变
- 屏幕宽度被均匀分成 7 个区域，每个区域对应一种颜色
- 适用于横向滚动文本的渐变效果

**颜色组合：**

- **组合 1**：红 → 橙 → 黄 → 白 → 黄 → 橙 → 红（火焰渐变，对称设计）
- **组合 2**：粉红 → 橙 → 黄 → 绿 → 青 → 蓝 → 紫（霓虹色渐变）
- **组合 3**：深绿 → 浅绿 → 黄绿 → 白 → 黄绿 → 浅绿 → 深绿（森林渐变，对称设计）

### 3. 45 度斜渐变模式

**模式标识：** `BT_GRADIENT_DIAGONAL_45_1`、`BT_GRADIENT_DIAGONAL_45_2`、`BT_GRADIENT_DIAGONAL_45_3`

**计算逻辑：**

```cpp
else if (gradientMode >= BT_GRADIENT_DIAGONAL_45_1 && gradientMode <= BT_GRADIENT_DIAGONAL_45_3) {
    // 45度渐变：根据对角线位置计算颜色索引（左上到右下）
    int diagonalPosition = x + y;
    int maxDiagonal = currentScreen.width + currentScreen.height - 2;
    colorIndex = (diagonalPosition * 7) / maxDiagonal; // 对角线距离分成7块
}
```

**显示效果：**

- 颜色沿着从左上角到右下角的对角线方向渐变
- 对角线距离被均匀分成 7 个区域，每个区域对应一种颜色
- 创造出独特的斜向渐变视觉效果

**数学原理：**

- 使用 `x + y` 计算像素到左上角的曼哈顿距离
- 最大对角线距离为 `width + height - 2`
- 通过比例计算确定颜色索引

**颜色组合：**

- **组合 1**：紫 → 蓝 → 青 → 绿 → 黄 → 橙 → 红（日落渐变）
- **组合 2**：深蓝 → 青 → 绿 → 白 → 绿 → 青 → 深蓝（海洋渐变，对称设计）
- **组合 3**：粉 → 紫 → 蓝 → 青 → 绿 → 黄 → 橙（霓虹渐变）

## 渐变色应用函数

### 16x16 字符渐变显示

```cpp
// 普通渐变字符
void drawChar16x16Gradient(int x, int y, const uint16_t *font_data, bool isUpper, uint8_t gradientMode);

// 竖向渐变字符（旋转90度）
void drawChar16x16VerticalGradient(int x, int y, const uint16_t *font_data, bool isUpper, uint8_t gradientMode);

// 渐变字符串
void drawString16x16Gradient(int x, int y, const uint16_t *font_array, int char_count, bool isUpper, uint8_t gradientMode);

// 竖向渐变字符串
void drawString16x16VerticalGradient(int x, int y, const uint16_t *font_array, int char_count, bool isUpper, uint8_t gradientMode);
```

**渐变字符绘制核心代码：**

```cpp
void drawChar16x16Gradient(int x, int y, const uint16_t *font_data, bool isUpper, uint8_t gradientMode)
{
    // 使用列取模方式：每个uint16_t代表一列的16个像素
    for (int col = 0; col < 16; col++)
    {
        uint16_t col_data = font_data[col];
        for (int row = 0; row < 16; row++)
        {
            // 检查对应位是否为1（从高位开始，高位对应上方像素）
            if (col_data & (0x8000 >> row))
            {
                int px = x + col;
                int py = y + row;
                if (px >= 0 && px < PANEL_RES_X && py >= 0 && py < PANEL_RES_Y)
                {
                    // 根据像素位置获取渐变色
                    uint16_t color = getGradientColor(px, py, isUpper, gradientMode);
                    bool isOverlayMode = (currentloopstate == loopstate::loop_state_overlay );

                    if (isOverlayMode) {
                        // 叠层模式：直接使用全屏坐标进行智能绘制
                        EnhancedTextPixelMask::smartDrawTextPixel(px, py, color);
                    } else {
                        // 分区模式和普通模式：使用原有坐标变换
                        drawTextPixel(px, py, color);
                    }
                }
            }
        }
    }
}
```

### 32x32 字符渐变显示

```cpp
// 32x32渐变字符
void drawChar32x32Gradient(int x, int y, const uint16_t *font_data, uint8_t gradientMode);

// 32x32竖向渐变字符
void drawChar32x32VerticalGradient(int x, int y, const uint16_t *font_data, uint8_t gradientMode);

// 32x32渐变字符串
void drawString32x32Gradient(int x, int y, const uint16_t *font_array, int char_count, uint8_t gradientMode);

// 32x32竖向渐变字符串
void drawString32x32VerticalGradient(int x, int y, const uint16_t *font_array, int char_count, uint8_t gradientMode);
```

### 渐变色获取函数

```cpp
// 16x16模式渐变色获取
uint16_t getGradientColor(int x, int y, bool isUpper, uint8_t gradientMode);

// 32x32模式渐变色获取（内部调用16x16函数）
uint16_t getGradientColor32x32(int x, int y, uint8_t gradientMode) {
    return getGradientColor(x, y, true, gradientMode);
}
```

## 技术实现细节

### 颜色格式转换

```cpp
// RGB888转RGB565颜色格式
uint16_t rgb888to565(uint8_t r, uint8_t g, uint8_t b) {
    return ((r & 0xF8) << 8) | ((g & 0xFC) << 3) | (b >> 3);
}
```

### 索引边界保护

```cpp
// 确保索引在有效范围内
if (colorIndex < 0) colorIndex = 0;
if (colorIndex >= 7) colorIndex = 6;
```

### 动态屏幕适配

系统支持动态屏幕尺寸，通过 `currentScreen.width` 和 `currentScreen.height` 自动适配不同的显示区域。

### 双屏模式支持

在 16x16 双屏模式下，上下两个屏幕可以独立进行渐变显示：

- 上半屏：`isUpper = true`
- 下半屏：`isUpper = false`，Y 坐标自动调整为相对坐标

### 字符旋转支持

支持文字的竖向显示（向左旋转 90 度），渐变效果会相应地进行坐标变换，确保渐变方向的正确性。

---

## 配置文件中的渐变模式定义

在 `config.h` 文件中定义了所有渐变模式的常量：

```cpp
/* ------------------------------------------------------------------------
 * 渐变色模式定义
 * ------------------------------------------------------------------------ */
#define BT_GRADIENT_FIXED 0x00        // 固定色（无渐变）
#define BT_GRADIENT_VERTICAL_1 0x01   // 上下渐变组合1
#define BT_GRADIENT_VERTICAL_2 0x02   // 上下渐变组合2
#define BT_GRADIENT_VERTICAL_3 0x03   // 上下渐变组合3
#define BT_GRADIENT_HORIZONTAL_1 0x04 // 左右渐变组合1
#define BT_GRADIENT_HORIZONTAL_2 0x05 // 左右渐变组合2
#define BT_GRADIENT_HORIZONTAL_3 0x06 // 左右渐变组合3
#define BT_GRADIENT_DIAGONAL_45_1 0x07 // 45度渐变组合1
#define BT_GRADIENT_DIAGONAL_45_2 0x08 // 45度渐变组合2
#define BT_GRADIENT_DIAGONAL_45_3 0x09 // 45度渐变组合3

/* ------------------------------------------------------------------------
 * 颜色模式类型定义
 * ------------------------------------------------------------------------ */
#define COLOR_MODE_FIXED 0x00    // 固定色模式
#define COLOR_MODE_GRADIENT 0x01 // 渐变色模式
#define COLOR_MODE_SPECIFIC 0x02 // 特定字符颜色模式
#define COLOR_MODE_RANDOM 0x03   // 随机颜色模式
```

## 渐变模式映射表

| 模式值 | 模式名称                  | 渐变方向 | 颜色组合索引 | 描述           |
| ------ | ------------------------- | -------- | ------------ | -------------- |
| 0x00   | BT_GRADIENT_FIXED         | -        | -            | 固定色，无渐变 |
| 0x01   | BT_GRADIENT_VERTICAL_1    | 上下     | 0            | 红橙黄绿青蓝紫 |
| 0x02   | BT_GRADIENT_VERTICAL_2    | 上下     | 1            | 明亮彩虹色     |
| 0x03   | BT_GRADIENT_VERTICAL_3    | 上下     | 2            | 紫蓝青绿黄橙红 |
| 0x04   | BT_GRADIENT_HORIZONTAL_1  | 左右     | 3            | 火焰渐变       |
| 0x05   | BT_GRADIENT_HORIZONTAL_2  | 左右     | 4            | 明亮霓虹色     |
| 0x06   | BT_GRADIENT_HORIZONTAL_3  | 左右     | 5            | 森林渐变       |
| 0x07   | BT_GRADIENT_DIAGONAL_45_1 | 45 度斜  | 6            | 日落渐变       |
| 0x08   | BT_GRADIENT_DIAGONAL_45_2 | 45 度斜  | 7            | 海洋渐变       |
| 0x09   | BT_GRADIENT_DIAGONAL_45_3 | 45 度斜  | 8            | 霓虹渐变       |

**注意事项：**

1. 所有渐变计算都是实时进行的，每个像素都会根据其位置计算出对应的颜色
2. 颜色索引计算使用整数除法，可能存在轻微的量化误差
3. 系统会自动处理边界情况，确保颜色索引在有效范围内
4. 渐变效果与屏幕分辨率和字体大小相关，不同配置下的视觉效果会有所差异
5. 每种渐变模式都有 3 个不同的颜色组合可供选择，提供丰富的视觉效果

本文档涵盖了渐变色系统的所有核心功能和实现细节，为理解和维护渐变色显示逻辑提供了全面的参考。
