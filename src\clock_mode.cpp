#include "clock_mode.h"
#include "score_mode.h"
#include "gif_player.h"

// ==================== 从gif_player.cpp移动的extern声明 ====================

// 时间显示控制标志由score_mode.cpp管理 - 从gif_player.cpp第16行移动
extern bool time_display_enabled;

// 时钟模式状态管理变量定义（避免链接问题）
bool clock_mode_active = false;

// ==================== 大屏模式全局状态管理变量定义 ====================
// 从score_mode.h移动到这里，避免多重定义错误
DisplayMode current_display_mode = DISPLAY_MODE_SMALL;  // 默认小屏模式，保持向后兼容
BigScreenState big_screen_state = BIG_SCREEN_TIME;
unsigned long big_screen_last_switch = 0;
unsigned long big_screen_switch_interval = 5000;  // 默认5秒切换
WeekdayLanguage big_screen_language = LANG_ENGLISH;
uint16_t big_screen_time_color = COLOR_RED;
uint16_t big_screen_weekday_color = COLOR_BLUE;
uint16_t big_screen_bg_color = COLOR_BLACK;

// 当前显示语言变量定义（在clock_mode.h中有extern声明）
WeekdayLanguage current_display_language = LANG_CHINESE;  // 默认中文

// ==================== 从gif_player.cpp移动的全局变量定义 ====================

// 显示区域相关变量定义 - 从gif_player.cpp第562-563行移动
DisplayRegion display_regions[MAX_REGIONS];
TimeDisplayInfo time_info;

// ==================== 从gif_player.cpp移动的字体数据 ====================

// 基础字体数据 (数字0-9和冒号)8×16 - 从gif_player.h移动
// 使用extern关键字确保外部链接性，供score_mode.cpp使用
extern const unsigned char font_data[11][16] = {
    {  // 字符":"
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x04,
        0x00, 0x00, 0x00, 0x00, 0x04, 0x04, 0x00, 0x00
    },
    {  // 字符 '0'
        0x00, 0x00, 0x00, 0x18, 0x24, 0x42, 0x42, 0x42,
        0x42, 0x42, 0x42, 0x42, 0x24, 0x18, 0x00, 0x00
    },
    {  // 字符 '1'
        0x00, 0x00, 0x00, 0x10, 0x1C, 0x10, 0x10, 0x10,
        0x10, 0x10, 0x10, 0x10, 0x10, 0x7C, 0x00, 0x00
    },
    {  // 字符 '2'
        0x00, 0x00, 0x00, 0x3C, 0x42, 0x42, 0x42, 0x40,
        0x20, 0x10, 0x08, 0x04, 0x42, 0x7E, 0x00, 0x00
    },
    {  // 字符 '3'
        0x00, 0x00, 0x00, 0x3C, 0x42, 0x42, 0x40, 0x20,
        0x18, 0x20, 0x40, 0x42, 0x42, 0x3C, 0x00, 0x00
    },
    {  // 字符 '4'
        0x00, 0x00, 0x00, 0x20, 0x30, 0x30, 0x28, 0x24,
        0x24, 0x22, 0xFE, 0x20, 0x20, 0xF8, 0x00, 0x00
    },
    {  // 字符 '5'
        0x00, 0x00, 0x00, 0x7E, 0x02, 0x02, 0x02, 0x1E,
        0x22, 0x40, 0x40, 0x42, 0x22, 0x1C, 0x00, 0x00
    },
    {  // 字符 '6'
        0x00, 0x00, 0x00, 0x18, 0x24, 0x02, 0x02, 0x3A,
        0x46, 0x42, 0x42, 0x42, 0x44, 0x38, 0x00, 0x00
    },
    {  // 字符 '7'
        0x00, 0x00, 0x00, 0x7E, 0x42, 0x20, 0x20, 0x10,
        0x10, 0x08, 0x08, 0x08, 0x08, 0x08, 0x00, 0x00
    },
    {  // 字符 '8'
        0x00, 0x00, 0x00, 0x3C, 0x42, 0x42, 0x42, 0x24,
        0x18, 0x24, 0x42, 0x42, 0x42, 0x3C, 0x00, 0x00
    },
    {  // 字符 '9'
        0x00, 0x00, 0x00, 0x1C, 0x22, 0x42, 0x42, 0x42,
        0x62, 0x5C, 0x40, 0x40, 0x24, 0x18, 0x00, 0x00
    }
};

// 中文星期汉字数据 (16x16点阵) - 从gif_player.cpp第41-101行移动
const unsigned char chinese_weekday_font_data[9][32] = {
    { // "星" (Index 0)
        0x00, 0x00, 0xF8, 0x0F, 0x08, 0x08, 0xF8, 0x0F,
        0x08, 0x08, 0xF8, 0x0F, 0x80, 0x00, 0x88, 0x00,
        0xF8, 0x1F, 0x84, 0x00, 0x82, 0x00, 0xF8, 0x0F,
        0x80, 0x00, 0x80, 0x00, 0xFE, 0x3F, 0x00, 0x00
    },
    { // "期" (Index 1)
        0x44, 0x00, 0x44, 0x3E, 0xFE, 0x22, 0x44, 0x22,
        0x44, 0x22, 0x7C, 0x3E, 0x44, 0x22, 0x44, 0x22,
        0x7C, 0x22, 0x44, 0x3E, 0x44, 0x22, 0xFF, 0x22,
        0x20, 0x21, 0x44, 0x21, 0x82, 0x28, 0x41, 0x10
    },
    { // "一" (Index 2)
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0x7F,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
    },
    { // "二" (Index 3)
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFC, 0x1F,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0xFF, 0x7F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
    },
    { // "三" (Index 4)
        0x00, 0x00, 0x00, 0x00, 0xFE, 0x3F, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFC, 0x1F,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0xFF, 0x7F, 0x00, 0x00, 0x00, 0x00
    },
    { // "四" (Index 5)
        0x00, 0x00, 0x00, 0x00, 0xFE, 0x3F, 0x22, 0x22,
        0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22,
        0x12, 0x22, 0x12, 0x3C, 0x0A, 0x20, 0x06, 0x20,
        0x02, 0x20, 0xFE, 0x3F, 0x02, 0x20, 0x00, 0x00
    },
    { // "五" (Index 6)
        0x00, 0x00, 0xFE, 0x3F, 0x40, 0x00, 0x40, 0x00,
        0x40, 0x00, 0x40, 0x00, 0xFC, 0x0F, 0x20, 0x08,
        0x20, 0x08, 0x20, 0x08, 0x20, 0x08, 0x10, 0x08,
        0x10, 0x08, 0x10, 0x08, 0xFF, 0x7F, 0x00, 0x00
    },
    { // "六" (Index 7)
        0x40, 0x00, 0x80, 0x00, 0x00, 0x01, 0x00, 0x01,
        0x00, 0x00, 0xFF, 0x7F, 0x00, 0x00, 0x00, 0x00,
        0x20, 0x02, 0x20, 0x04, 0x10, 0x08, 0x10, 0x10,
        0x08, 0x10, 0x04, 0x20, 0x02, 0x20, 0x00, 0x00
    },
    { // "日" (Index 8)
        0x00, 0x00, 0xF8, 0x0F, 0x08, 0x08, 0x08, 0x08,
        0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0xF8, 0x0F,
        0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08,
        0x08, 0x08, 0x08, 0x08, 0xF8, 0x0F, 0x08, 0x08
    }
};

// 英文星期字体数据 (8x16点阵，每个星期显示前3个字母) - 从gif_player.cpp第100-178行移动
const unsigned char english_weekday_font_data[7][48] = {
    { // SUNDAY (SUN)
        // S
        0x00,0x00,0x00,0x3C,0x66,0x62,0x06,0x0C,
        0x38,0x60,0x42,0x42,0x66,0x3C,0x00,0x00,
        // U
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63,
        0x42, 0x42, 0x42, 0x42, 0x62, 0xDC, 0x00, 0x00,
        // N
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3B,
        0x46, 0x42, 0x42, 0x42, 0x42, 0xE7, 0x00, 0x00,
    },
    { // MONDAY (MON)
        // M
        0x00,0x00,0x00,0x66,0x66,0x66,0x66,0x76,
        0x5E,0x5A,0x5A,0x5A,0x5A,0x4A,0x00,0x00,
        // O
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3C,
        0x42, 0x42, 0x42, 0x42, 0x42, 0x3C, 0x00, 0x00,
        // N
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3B,
        0x46, 0x42, 0x42, 0x42, 0x42, 0xE7, 0x00, 0x00,
    },
    { // TUESDAY (TUE)
        // T
        0x00,0x00,0x00,0x7E,0x18,0x18,0x18,0x18,
        0x18,0x18,0x18,0x18,0x18,0x18,0x00,0x00,
        // U
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63,
        0x42, 0x42, 0x42, 0x42, 0x62, 0xDC, 0x00, 0x00,
        // E
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3C,
        0x42, 0x42, 0x7E, 0x02, 0x42, 0x3C, 0x00, 0x00,
    },
    { // WEDNESDAY (WED)
        // W
        0x00,0x00,0x00,0xD9,0x5B,0x5B,0x5A,0x5A,
        0x56,0x56,0x66,0x66,0x26,0x26,0x00,0x00,
        // E
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3C,
        0x42, 0x42, 0x7E, 0x02, 0x42, 0x3C, 0x00, 0x00,
        // D
        0x00, 0x00, 0x00, 0x00, 0x60, 0x40, 0x40, 0x7C,
        0x42, 0x42, 0x42, 0x42, 0x62, 0xDC, 0x00, 0x00,
    },
    { // THURSDAY (THU)
        // T
        0x00,0x00,0x00,0x7E,0x18,0x18,0x18,0x18,
        0x18,0x18,0x18,0x18,0x18,0x18,0x00,0x00,
        // H
        0x00, 0x00, 0x00, 0x00, 0x03, 0x02, 0x02, 0x3A,
        0x46, 0x42, 0x42, 0x42, 0x42, 0xE7, 0x00, 0x00,
        // U
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63,
        0x42, 0x42, 0x42, 0x42, 0x62, 0xDC, 0x00, 0x00,
    },
    { // FRIDAY (FRI)
        // F
        0x00,0x00,0x00,0x7E,0x02,0x02,0x02,0x02,
        0x3E,0x02,0x02,0x02,0x02,0x02,0x00,0x00,
        // R
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x77,
        0x4C, 0x04, 0x04, 0x04, 0x04, 0x1F, 0x00, 0x00,
        // I
        0x00, 0x00, 0x00, 0x0C, 0x0C, 0x00, 0x00, 0x0E,
        0x08, 0x08, 0x08, 0x08, 0x08, 0x3E, 0x00, 0x00
    },
    { // SATURDAY (SAT)
        // S
        0x00,0x00,0x00,0x3C,0x66,0x62,0x06,0x0C,
        0x38,0x60,0x42,0x42,0x66,0x3C,0x00,0x00,
        // A
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1C,
        0x22, 0x30, 0x2C, 0x22, 0x32, 0x6C, 0x00, 0x00,
        // T
        0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x08, 0x3E,
        0x08, 0x08, 0x08, 0x08, 0x48, 0x30, 0x00, 0x00
    }
};

// 32x16中文星期字体数据（星、期、日、一、二、三、四、五、六）- 从gif_player.cpp第286-359行移动
const unsigned char font_chinese[9][72] PROGMEM = {
  { // 星 (0)
    0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x08, 0x10, 0xF8, 0x1F, 0x08, 0x10,
    0x08, 0x10, 0x08, 0x10, 0xF8, 0x1F, 0x08, 0x10, 0x08, 0x10, 0x08, 0x10,
    0xF8, 0x1F, 0x08, 0x10, 0x88, 0x00, 0x10, 0x01, 0x90, 0x10, 0x88, 0x10,
    0xF8, 0x3F, 0x88, 0x00, 0x88, 0x00, 0x84, 0x00, 0x84, 0x08, 0xFA, 0x1F,
    0x82, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x20, 0x80, 0x60, 0x7E, 0x1F,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
  },
  { // 期 (1)
    0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0xC8, 0x00, 0x48, 0x7C, 0x48, 0x44,
    0x48, 0x45, 0xFE, 0x45, 0x48, 0x44, 0x48, 0x44, 0x48, 0x44, 0x78, 0x7C,
    0x48, 0x44, 0x48, 0x44, 0x48, 0x44, 0x48, 0x44, 0x78, 0x44, 0x48, 0x44,
    0x48, 0x44, 0x48, 0x7C, 0xFE, 0x43, 0x02, 0x42, 0x00, 0x42, 0x58, 0x42,
    0x88, 0x42, 0x88, 0x42, 0x84, 0x41, 0x04, 0x41, 0x02, 0x31, 0x82, 0x20,
    0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
  },
  { // 日 (2)
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0xF8, 0x1F, 0x08, 0x10,
    0x08, 0x10, 0x08, 0x10, 0x08, 0x10, 0x08, 0x10, 0x08, 0x10, 0x08, 0x10,
    0x08, 0x10, 0x08, 0x10, 0xF8, 0x1F, 0x08, 0x10, 0x08, 0x10, 0x08, 0x10,
    0x08, 0x10, 0x08, 0x10, 0x08, 0x10, 0x08, 0x10, 0x08, 0x10, 0x08, 0x10,
    0x08, 0x10, 0x08, 0x10, 0xF8, 0x1F, 0x08, 0x10, 0x08, 0x10, 0x08, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
  },
  { // 一 (3)
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x20, 0x00, 0x60, 0xFE, 0x7F, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
  },
  { // 二 (4)
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x10, 0xFC, 0x1F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x20, 0x00, 0x60, 0xFE, 0x7F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
  },
  { // 三 (5)
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0xFC, 0x3F, 0x04, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0xF8, 0x1F, 0x08, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0xFE, 0x7F, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
  },
  { // 四 (6)
    0x00, 0x00, 0x04, 0x20, 0xFC, 0x3F, 0x44, 0x22, 0x44, 0x22, 0x44, 0x22,
    0x44, 0x22, 0x44, 0x22, 0x44, 0x22, 0x44, 0x22, 0x44, 0x22, 0x44, 0x22,
    0x44, 0x22, 0x44, 0x22, 0x44, 0x22, 0x44, 0x22, 0x24, 0x22, 0x24, 0x22,
    0x24, 0x22, 0x24, 0x22, 0x24, 0x3E, 0x14, 0x3C, 0x14, 0x20, 0x0C, 0x20,
    0x04, 0x20, 0x04, 0x20, 0x04, 0x20, 0xFC, 0x3F, 0x04, 0x20, 0x04, 0x20,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
  },
  { // 五 (7)
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0xFC, 0x3F, 0x80, 0x00,
    0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x40, 0x00,
    0x40, 0x08, 0x40, 0x08, 0xF8, 0x0F, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08,
    0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x20, 0x08,
    0x20, 0x08, 0x20, 0x68, 0xFE, 0x7F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
  },
  { // 六 (8)
    0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x01,
    0x80, 0x01, 0x00, 0x01, 0x00, 0x01, 0x00, 0x00, 0x00, 0x60, 0xFE, 0x7F,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x02, 0x60, 0x02, 0x20, 0x04,
    0x20, 0x04, 0x20, 0x08, 0x30, 0x08, 0x10, 0x18, 0x10, 0x10, 0x08, 0x30,
    0x08, 0x30, 0x08, 0x20, 0x04, 0x20, 0x04, 0x20, 0x02, 0x20, 0x02, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
  }
};

// 32x8数字字体数据（冒号、0-9）- 从gif_player.cpp第362-429行移动
const unsigned char number_font[11][32] PROGMEM = {
  { // [0] = ':'
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x18, 0x18,
    0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18,
    0x18, 0x18, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00
  },
  { // [1] = '0'
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x24,
    0x24, 0x24, 0x42, 0x42, 0x42, 0x42, 0x42, 0x42,
    0x42, 0x42, 0x42, 0x42, 0x42, 0x42, 0x42, 0x24,
    0x24, 0x24, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00
  },
  { // [2] = '1'
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08,
    0x0E, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08,
    0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08,
    0x08, 0x08, 0x3E, 0x00, 0x00, 0x00, 0x00, 0x00
  },
  { // [3] = '2'
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x26,
    0x42, 0x42, 0x42, 0x42, 0x42, 0x40, 0x40, 0x20,
    0x20, 0x10, 0x10, 0x08, 0x08, 0x04, 0x44, 0x44,
    0x42, 0x7E, 0x7E, 0x00, 0x00, 0x00, 0x00, 0x00
  },
  { // [4] = '3'
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x26,
    0x42, 0x42, 0x42, 0x42, 0x40, 0x20, 0x20, 0x18,
    0x20, 0x20, 0x40, 0x40, 0x40, 0x42, 0x42, 0x42,
    0x22, 0x22, 0x1C, 0x00, 0x00, 0x00, 0x00, 0x00
  },
  { // [5] = '4'
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x20,
    0x30, 0x30, 0x28, 0x28, 0x28, 0x24, 0x24, 0x24,
    0x22, 0x22, 0x22, 0x22, 0x7E, 0x20, 0x20, 0x20,
    0x20, 0x20, 0x30, 0x78, 0x00, 0x00, 0x00, 0x00
  },
  { // [6] = '5'
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7E, 0x7E,
    0x02, 0x02, 0x02, 0x02, 0x02, 0x1A, 0x26, 0x22,
    0x42, 0x40, 0x40, 0x40, 0x40, 0x42, 0x42, 0x22,
    0x22, 0x22, 0x1C, 0x00, 0x00, 0x00, 0x00, 0x00
  },
  { // [7] = '6'
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x24,
    0x24, 0x22, 0x02, 0x02, 0x02, 0x02, 0x1A, 0x26,
    0x26, 0x42, 0x42, 0x42, 0x42, 0x42, 0x42, 0x42,
    0x24, 0x14, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00
  },
  { // [8] = '7'
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7E, 0x7E,
    0x22, 0x22, 0x22, 0x20, 0x10, 0x10, 0x10, 0x10,
    0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08,
    0x08, 0x08, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00
  },
  { // [9] = '8'
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x24,
    0x42, 0x42, 0x42, 0x42, 0x42, 0x46, 0x24, 0x1C,
    0x18, 0x34, 0x24, 0x42, 0x42, 0x42, 0x42, 0x42,
    0x42, 0x24, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00
  },
  { // [10] = '9'
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x14,
    0x24, 0x22, 0x42, 0x42, 0x42, 0x42, 0x42, 0x42,
    0x62, 0x54, 0x58, 0x40, 0x40, 0x40, 0x20, 0x24,
    0x24, 0x24, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00
  }
};

// 32高×16宽英文星期字体数据（21个字符：每个星期前三个字母的点阵数据）- 从gif_player.cpp第432-559行移动
const unsigned char en_font[21][64] PROGMEM = {
  { // 0: S
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xF0,0x13,0x18,0x1E,
        0x0C,0x18,0x06,0x18,0x06,0x10,0x06,0x10,0x06,0x00,0x0E,0x00,0x3C,0x00,0xF8,0x00,
        0xE0,0x03,0x80,0x0F,0x00,0x1E,0x00,0x18,0x00,0x38,0x02,0x30,0x02,0x30,0x06,0x30,
        0x04,0x30,0x0C,0x18,0x1C,0x0C,0xE4,0x07,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    },
    { // 1: u
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x10,0x10,0x1E,0x1E,0x18,0x18,0x18,0x18,
        0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,
        0x18,0x18,0x18,0x1C,0x30,0x7A,0xE0,0x09,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    },
    { // 2: n
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x90,0x07,0x5E,0x0C,0x38,0x18,
        0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,
        0x18,0x18,0x18,0x18,0x18,0x18,0x7E,0x7E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    },
    { // 3: M
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0F,0xF0,0x1C,0x38,
        0x1C,0x38,0x1C,0x38,0x1C,0x38,0x1C,0x34,0x34,0x34,0x34,0x34,0x34,0x34,0x34,0x32,
        0x34,0x32,0x64,0x32,0x64,0x32,0x64,0x32,0x64,0x31,0x44,0x31,0xC4,0x31,0xC4,0x31,
        0xC4,0x30,0xC4,0x30,0x84,0x30,0x8F,0xFC,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    },
    { // 4: o
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xC0,0x03,0x30,0x0C,0x10,0x18,
        0x18,0x18,0x08,0x30,0x0C,0x30,0x0C,0x30,0x0C,0x30,0x0C,0x30,0x0C,0x30,0x18,0x18,
        0x18,0x18,0x30,0x0C,0xC0,0x03,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    },
    { // 5: n (同2)
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x90,0x07,0x5E,0x0C,0x38,0x18,
        0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,
        0x18,0x18,0x18,0x18,0x18,0x18,0x7E,0x7E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    },
    { // 6: T
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFC,0x3F,0x8C,0x21,
        0x84,0x61,0x82,0x41,0x82,0x41,0x80,0x01,0x80,0x00,0x80,0x01,0x80,0x01,0x80,0x01,
        0x80,0x01,0x80,0x01,0x80,0x01,0x80,0x01,0x80,0x01,0x80,0x01,0x80,0x01,0x80,0x01,
        0x80,0x01,0x80,0x01,0x80,0x01,0xE0,0x07,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    },
    { // 7: u (第二个u)
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x10,0x10,0x1E,0x1E,0x18,0x18,0x18,0x18,
        0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,
        0x18,0x18,0x18,0x1C,0x30,0x7A,0xE0,0x09,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    },
    { // 8: e
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xC0,0x03,0x30,0x0C,0x10,0x18,
        0x18,0x10,0x0C,0x30,0x0C,0x30,0x0C,0x30,0xFC,0x3F,0x0C,0x00,0x0C,0x00,0x0C,0x00,
        0x18,0x20,0x18,0x10,0x70,0x18,0xC0,0x07,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    },
    { // 9: W
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xCF,0xF3,0x86,0x61,
        0x86,0x21,0x84,0x21,0x04,0x21,0x0C,0x23,0x8C,0x23,0x8C,0x23,0x8C,0x13,0x8C,0x13,
        0x88,0x13,0x48,0x12,0x58,0x16,0x58,0x16,0x58,0x0E,0x38,0x0E,0x30,0x0E,0x30,0x0E,
        0x30,0x0C,0x30,0x04,0x10,0x04,0x10,0x04,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    },
    { // 10: e (第二个e)
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xC0,0x03,0x30,0x0C,0x10,0x18,
        0x18,0x10,0x0C,0x30,0x0C,0x30,0x0C,0x30,0xFC,0x3F,0x0C,0x00,0x0C,0x00,0x0C,0x00,
        0x18,0x20,0x18,0x10,0x70,0x18,0xC0,0x07,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    },
    { // 11: d
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x10,0x00,0x1E,0x00,0x18,
        0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0xE0,0x1B,0x30,0x1C,0x18,0x18,
        0x18,0x18,0x0C,0x18,0x0C,0x18,0x0C,0x18,0x0C,0x18,0x0C,0x18,0x0C,0x18,0x08,0x18,
        0x18,0x1C,0x30,0x7A,0xE0,0x09,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    },
    { // 12: T
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFC,0x3F,0x8C,0x21,
        0x84,0x61,0x82,0x41,0x82,0x41,0x80,0x01,0x80,0x01,0x80,0x01,0x80,0x01,0x80,0x01,
        0x80,0x01,0x80,0x01,0x80,0x01,0x80,0x01,0x80,0x01,0x80,0x01,0x80,0x01,0x80,0x01,
        0x80,0x01,0x80,0x01,0x80,0x01,0xE0,0x07,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    },
    { // 13: h
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x10,0x00,0x1E,0x00,0x18,0x00,
        0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x98,0x07,0x58,0x0C,0x38,0x18,
        0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,
        0x18,0x18,0x18,0x18,0x18,0x18,0x7E,0x7E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    },
    { // 14: u (第三个u)
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x10,0x10,0x1E,0x1E,0x18,0x18,0x18,0x18,
        0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,
        0x18,0x18,0x18,0x1C,0x30,0x7A,0xE0,0x09,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    },
    { // 15: F
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFE,0x3F,0x18,0x38,
        0x18,0x20,0x18,0x40,0x18,0x40,0x18,0x00,0x18,0x00,0x18,0x08,0x18,0x08,0x18,0x0C,
        0xF8,0x0F,0x18,0x0C,0x18,0x08,0x18,0x08,0x18,0x08,0x18,0x00,0x18,0x00,0x18,0x00,
        0x18,0x00,0x18,0x00,0x18,0x00,0x7E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    },
    { // 16: r
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x38,0x7E,0x66,0x60,0x61,
        0xE0,0x01,0xE0,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,
        0x60,0x00,0x60,0x00,0x60,0x00,0xFE,0x07,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    },
    { // 17: i
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x80,0x01,0xC0,0x03,
        0x80,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0xF8,0x01,0x80,0x01,0x80,0x01,
        0x80,0x01,0x80,0x01,0x80,0x01,0x80,0x01,0x80,0x01,0x80,0x01,0x80,0x01,0x80,0x01,
        0x80,0x01,0x80,0x01,0x80,0x01,0xF8,0x1F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    },
    { // 18: S (第二个S)
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xF0,0x13,0x18,0x1E,
        0x0C,0x18,0x06,0x18,0x06,0x10,0x06,0x10,0x06,0x00,0x0E,0x00,0x3C,0x00,0xF8,0x00,
        0xE0,0x03,0x80,0x0F,0x00,0x1E,0x00,0x18,0x00,0x38,0x02,0x30,0x02,0x30,0x06,0x30,
        0x04,0x30,0x0C,0x18,0x1C,0x0C,0xE4,0x07,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    },
    { // 19: a
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xE0,0x07,0x18,0x0C,0x0C,0x18,
        0x0C,0x18,0x0C,0x18,0x00,0x1C,0xE0,0x1B,0x38,0x18,0x0C,0x18,0x06,0x18,0x06,0x18,
        0x06,0x18,0x06,0x98,0x0C,0x9E,0xF8,0x71,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    },
    { // 20: t
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
        0x80,0x00,0x80,0x00,0x80,0x00,0xC0,0x00,0xE0,0x00,0xFC,0x1F,0xC0,0x00,0xC0,0x00,
        0xC0,0x00,0xC0,0x00,0xC0,0x00,0xC0,0x00,0xC0,0x00,0xC0,0x00,0xC0,0x00,0xC0,0x00,
        0xC0,0x20,0xC0,0x20,0x80,0x11,0x00,0x0F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    }
};

// ==================== 从gif_player.cpp移动的时钟相关函数 ====================

// 初始化显示区域系统 - 从gif_player.cpp第783-801行移动
void initDisplayRegions() {
    if(!time_display_enabled)return;
    // // 初始化GIF区域 (左侧 16x32)
    // initDisplayRegion(&display_regions[REGION_GIF], 0, 0, 31, 31, COLOR_WHITE, COLOR_BLACK);

    // // 初始化时间区域 (右上 48x16)
    // initDisplayRegion(&display_regions[REGION_TIME], 32, 0, 79, 15, COLOR_RED, COLOR_BLACK);

    // // 初始化星期区域 (右下 48x16)
    // initDisplayRegion(&display_regions[REGION_WEEKDAY], 32, 16, 79, 31, COLOR_BLUE, COLOR_BLACK);

    // // 初始化大屏区域 (右侧 48x32) - 大屏模式使用
    // initDisplayRegion(&display_regions[REGION_BIG_SCREEN], 32, 0, 79, 31, COLOR_BLUE, COLOR_BLACK);
      // 初始化GIF区域 (左侧 16x32)
    initDisplayRegion(&display_regions[REGION_GIF], 0, 0, 15, 31, COLOR_WHITE, COLOR_BLACK);

    // 初始化时间区域 (右上 48x16)
    initDisplayRegion(&display_regions[REGION_TIME], 16, 0, 63, 15, COLOR_RED, COLOR_BLACK);

    // 初始化星期区域 (右下 48x16)
    initDisplayRegion(&display_regions[REGION_WEEKDAY], 16, 16, 63, 31, COLOR_BLUE, COLOR_BLACK);

    // 初始化大屏区域 (右侧 48x32) - 大屏模式使用
    initDisplayRegion(&display_regions[REGION_BIG_SCREEN], 16, 0, 63, 31, COLOR_BLUE, COLOR_BLACK);

    // 根据当前模式设置区域可见性
    updateRegionVisibility();

    printf("Display regions initialized: Small=%d, Big=%d\n", DISPLAY_MODE_SMALL, DISPLAY_MODE_BIG);
}

// 设置时间显示区域 - 从gif_player.cpp第804-824行移动
void setupTimeRegions() {
    if(!time_display_enabled)return;
    // 初始化时间信息
    time_info.hour = DEFAULT_HOUR;
    time_info.minute = DEFAULT_MINUTE;
    time_info.weekday = 1;  // 默认周一
    time_info.time_color = COLOR_RED;
    time_info.weekday_color = COLOR_BLUE;

    // 清除时间和星期区域
    clearRegion(&display_regions[REGION_TIME]);
    clearRegion(&display_regions[REGION_WEEKDAY]);

    // 绘制初始时间
    drawTimeToRegion(&display_regions[REGION_TIME], time_info.hour, time_info.minute);

    // 绘制初始星期 (使用当前全局语言设置)
    drawWeekdayToRegionWithLang(&display_regions[REGION_WEEKDAY], time_info.weekday, LANG_CHINESE);

    Serial.printf("Time regions setup: %02d:%02d\n", time_info.hour, time_info.minute);
}

// 初始化单个显示区域 - 从gif_player.cpp第827-843行移动
void initDisplayRegion(DisplayRegion* region, uint8_t x1, uint8_t y1,
                      uint8_t x2, uint8_t y2, uint16_t text_color, uint16_t bg_color) {
    region->x1 = x1;
    region->y1 = y1;
    region->x2 = x2;
    region->y2 = y2;
    region->cursor_x = 0;
    region->cursor_y = 0;
    region->text_color = text_color;
    region->bg_color = bg_color;
    region->auto_clear = true;
    region->visible = true;
    region->update_flag = 1;
    region->bitmap_data = nullptr;
    region->data_width = 0;
    region->data_height = 0;
}

// 清除固定区域 - 从gif_player.cpp第846-854行移动
void clearRegion(DisplayRegion* region) {
    if (!region->visible || !dma_display) return;

      uint16_t bgColor = COLOR_BLACK;  // 可配置的背景色
             dma_display->fillRect(region->x1,region->y1,
                          region->x2, region->y2,
                         bgColor);
}
//清除可选区域
 void forceClearRegion(int16_t x1, int16_t y1, int16_t w, int16_t h) 
       {
                if ( !dma_display) return;
                // 逐像素清除整个区域
              uint16_t bgColor = COLOR_BLACK;  // 可配置的背景色
             dma_display->fillRect(x1, y1,
                          w, h,
                         bgColor);
       }

// 检查点是否在区域内 - 从gif_player.cpp第857-859行移动
bool isPointInRegion(DisplayRegion* region, uint8_t x, uint8_t y) {
    return (x >= region->x1 && x <= region->x2 && y >= region->y1 && y <= region->y2);
}

// 在区域内绘制字符 - 从gif_player.cpp第862-884行移动
void drawCharToRegion(DisplayRegion* region, uint8_t char_index, int8_t x, int8_t y) {
    if (!region->visible || !dma_display || char_index >= 11) return;

    const uint8_t* char_data = font_data[char_index];

    for (int row = 0; row < FONT_HEIGHT; row++) {
        uint8_t line_data = char_data[row];
        for (int col = 0; col < FONT_WIDTH; col++) {
            int pixel_x = region->x1 + x + col;
            int pixel_y = region->y1 + y + row;

            // 边界检查
            if (pixel_x > region->x2 || pixel_y > region->y2 ||
                pixel_x < region->x1 || pixel_y < region->y1) continue;

            // 检查像素是否需要点亮 (修复镜像问题，从右到左读取位)
            if (line_data & (0x01 << col)) {
               dma_display->drawPixel(pixel_x, pixel_y, region->text_color);
            }
            // 删除auto_clear分支，不再绘制背景像素
        }
    }
}

// 在区域内绘制星期汉字 (16x16字体) - 从gif_player.cpp第887-929行移动
void drawWeekdayCharToRegion(DisplayRegion* region, uint8_t char_index, int8_t x, int8_t y) {
    if (!region->visible || !dma_display || char_index >= 9) return;

    const uint8_t* char_data = chinese_weekday_font_data[char_index];

    for (int row = 0; row < WEEKDAY_FONT_HEIGHT; row++) {
        // 每行2个字节 (16位)
        uint8_t byte1 = char_data[row * 2];     // 低字节
        uint8_t byte2 = char_data[row * 2 + 1]; // 高字节

        // 先绘制低字节的8位
        for (int col = 0; col < 8; col++) {
            int pixel_x = region->x1 + x + col;
            int pixel_y = region->y1 + y + row;

            // 边界检查
            if (pixel_x > region->x2 || pixel_y > region->y2 ||
                pixel_x < region->x1 || pixel_y < region->y1) continue;

            // 检查像素是否需要点亮
            if (byte1 & (0x01 << col)) {
               dma_display->drawPixel(pixel_x, pixel_y, region->text_color);
            }
            // 删除auto_clear分支，不再绘制背景像素
        }

        // 再绘制高字节的8位
        for (int col = 0; col < 8; col++) {
            int pixel_x = region->x1 + x + col + 8;
            int pixel_y = region->y1 + y + row;

            // 边界检查
            if (pixel_x > region->x2 || pixel_y > region->y2 ||
                pixel_x < region->x1 || pixel_y < region->y1) continue;

            // 检查像素是否需要点亮
            if (byte2 & (0x01 << col)) {
                dma_display->drawPixel(pixel_x, pixel_y, region->text_color);
            }
            // 删除auto_clear分支，不再绘制背景像素
        }
    }
}

// 在区域内绘制英文星期字符 (8x16字体) - 从gif_player.cpp第932-959行移动
void drawEnglishWeekdayCharToRegion(DisplayRegion* region, uint8_t char_index, int8_t x, int8_t y) {
    if (!region->visible || !dma_display || char_index >= 7) return;

    const uint8_t* char_data = english_weekday_font_data[char_index];

    // 绘制3个字符 (每个星期的前3个字母)
    for (int char_offset = 0; char_offset < 3; char_offset++) {
        const uint8_t* single_char_data = char_data + (char_offset * 16);

        for (int row = 0; row < FONT_HEIGHT; row++) {
            uint8_t line_data = single_char_data[row];
            for (int col = 0; col < FONT_WIDTH; col++) {
                int pixel_x = region->x1 + x + (char_offset * FONT_WIDTH) + col;
                int pixel_y = region->y1 + y + row;

                // 边界检查
                if (pixel_x > region->x2 || pixel_y > region->y2 ||
                    pixel_x < region->x1 || pixel_y < region->y1) continue;

                // 检查像素是否需要点亮
                if (line_data & (0x01 << col)) {
                    dma_display->drawPixel(pixel_x, pixel_y, region->text_color);
                }
                // 删除auto_clear分支，不再绘制背景像素
            }
        }
    }
}

// 在区域内绘制时间 - 从gif_player.cpp第962-992行移动
void drawTimeToRegion(DisplayRegion* region, uint8_t hour, uint8_t minute) {
    if (!region->visible) return;

    // 智能清除区域（恢复GIF背景）
    forceClearRegion(region->x1, region->y1, region->x2, region->y2);

    // 计算显示位置 (居中显示)
    int8_t start_x = 4;  // 左边留4像素边距
    int8_t start_y = 0;  // 顶部对齐

    // 绘制小时 (两位数)
    uint8_t hour_tens = hour / 10;
    uint8_t hour_ones = hour % 10;

    if (hour_tens > 0) {
        drawCharToRegion(region, hour_tens + 1, start_x, start_y);  // +1因为索引1是'0'
    }
    drawCharToRegion(region, hour_ones + 1, start_x + FONT_WIDTH, start_y);

    // 绘制冒号
    drawCharToRegion(region, 0, start_x + FONT_WIDTH * 2, start_y);  // 索引0是':'

    // 绘制分钟 (两位数)
    uint8_t minute_tens = minute / 10;
    uint8_t minute_ones = minute % 10;

    drawCharToRegion(region, minute_tens + 1, start_x + FONT_WIDTH * 3, start_y);
    drawCharToRegion(region, minute_ones + 1, start_x + FONT_WIDTH * 4, start_y);
}

// 在区域内绘制星期 - 从gif_player.cpp第995-1022行移动
void drawWeekdayToRegion(DisplayRegion* region, uint8_t weekday) {
    if (!region->visible || weekday > 6) return;

    // 智能清除区域（恢复GIF背景）
   forceClearRegion(region->x1, region->y1, region->x2, region->y2);


    // 绘制"星期"两个字 (固定位置)
    drawWeekdayCharToRegion(region, 0, 0, 0);   // "星" 字
    drawWeekdayCharToRegion(region, 1, 16, 0);  // "期" 字

    // 根据星期数绘制对应的数字汉字
    uint8_t weekday_char_index;
    switch (weekday) {
        case 0: weekday_char_index = 8; break;  // 周日 -> "日"
        case 1: weekday_char_index = 2; break;  // 周一 -> "一"
        case 2: weekday_char_index = 3; break;  // 周二 -> "二"
        case 3: weekday_char_index = 4; break;  // 周三 -> "三"
        case 4: weekday_char_index = 5; break;  // 周四 -> "四"
        case 5: weekday_char_index = 6; break;  // 周五 -> "五"
        case 6: weekday_char_index = 7; break;  // 周六 -> "六"
        default: weekday_char_index = 2; break; // 默认 -> "一"
    }

    // 绘制星期数字 (第三个字的位置)
    drawWeekdayCharToRegion(region, weekday_char_index, 32, 0);
}

// 在区域内绘制星期 (支持多语言) - 从gif_player.cpp第1025-1056行移动
void drawWeekdayToRegionWithLang(DisplayRegion* region, uint8_t weekday, WeekdayLanguage language) {
    if (!region->visible || weekday > 6) return;

   forceClearRegion(region->x1, region->y1, region->x2, region->y2);

    switch (language) {
        case LANG_CHINESE:
            // 使用原有的中文显示逻辑
            drawWeekdayToRegion(region, weekday);
            break;

        case LANG_ENGLISH:
            // 绘制英文星期缩写 (3个字母，居中显示)
            {
                int region_width = region->x2 - region->x1 + 1;
                int text_width = 3 * FONT_WIDTH;  // 3个字符的宽度
                int start_x = (region_width - text_width) / 2;
                if (start_x < 0) start_x = 0;

                drawEnglishWeekdayCharToRegion(region, weekday, start_x, 0);
            }
            break;

        default:
            // 默认使用中文
            drawWeekdayToRegion(region, weekday);
            break;
    }
}

// 更新时间逻辑 - 从gif_player.cpp第1059-1109行移动
void updateTimeLogic() {
    static unsigned long last_minute_update = 0;
    static uint8_t last_displayed_minute = 0xFF;
    static uint8_t last_displayed_hour = 0xFF;
    static uint8_t last_displayed_weekday = 0xFF;

    unsigned long now = millis();

    // 每分钟更新一次显示
    if (now - last_minute_update >= 60000) {  // 60秒 = 60000毫秒
        time_info.minute++;
        if (time_info.minute >= 60) {
            time_info.minute = 0;
            time_info.hour++;
            if (time_info.hour >= 24) {
                time_info.hour = 0;
                // 新的一天开始，更新星期
                time_info.weekday++;
                if (time_info.weekday > 6) {
                    time_info.weekday = 0;  // 周日重新开始
                }
                Serial.printf("New day! Weekday changed to: %d\n", time_info.weekday);
            }
        }
        last_minute_update = now;
    }

    // 检查是否需要更新时间显示
    if (time_info.minute != last_displayed_minute || time_info.hour != last_displayed_hour) {
        drawTimeToRegion(&display_regions[REGION_TIME], time_info.hour, time_info.minute);
        last_displayed_minute = time_info.minute;
        last_displayed_hour = time_info.hour;

        Serial.printf("Time updated: %02d:%02d\n", time_info.hour, time_info.minute);
    }

    // 检查是否需要更新星期显示
    if (time_info.weekday != last_displayed_weekday) {
        // 使用当前全局语言设置进行显示
        drawWeekdayToRegionWithLang(&display_regions[REGION_WEEKDAY], time_info.weekday, current_display_language);
        last_displayed_weekday = time_info.weekday;

        // 根据当前语言输出调试信息
        const char* weekday_name = getWeekdayNameWithLang(time_info.weekday, current_display_language);
        if (current_display_language == LANG_CHINESE) {
            Serial.printf("Weekday updated: 星期%s\n", weekday_name);
        } else {
            Serial.printf("Weekday updated: %s\n", weekday_name);
        }
    }
}

// 更新时间显示 - 从gif_player.cpp第1112-1114行移动
void updateTimeDisplay() {
    updateTimeLogic();
}

// 更新所有区域 - 从gif_player.cpp第1117-1121行移动
void updateAllRegions() {
    // 时间区域在updateTimeDisplay中已经处理
    // GIF区域由原有的updateGIF()函数处理
    // 星期区域暂时不需要频繁更新
}

// 设置时间 - 从gif_player.cpp第1124-1131行移动
void setTime(uint8_t hour, uint8_t minute) {
    if (hour < 24 && minute < 60) {
        time_info.hour = hour;
        time_info.minute = minute;
        drawTimeToRegion(&display_regions[REGION_TIME], hour, minute);
        Serial.printf("Time set to: %02d:%02d\n", hour, minute);
    }
}

// 同时设置时间和星期 (使用当前全局语言设置，保持向后兼容) - 从gif_player.cpp第1134-1150行移动
void setTimeAndWeekday(uint8_t hour, uint8_t minute, uint8_t weekday) {
    if (hour < 24 && minute < 60 && weekday <= 6) {
        time_info.hour = hour;
        time_info.minute = minute;
        time_info.weekday = weekday;

        drawTimeToRegion(&display_regions[REGION_TIME], hour, minute);
        // 使用当前全局语言设置进行显示
        drawWeekdayToRegionWithLang(&display_regions[REGION_WEEKDAY], weekday, current_display_language);

        // 根据当前语言输出调试信息
        const char* weekday_name = getWeekdayNameWithLang(weekday, current_display_language);
        if (current_display_language == LANG_CHINESE) {
            Serial.printf("Time and weekday set to: %02d:%02d 星期%s\n", hour, minute, weekday_name);
        } else {
            Serial.printf("Time and weekday set to: %02d:%02d %s\n", hour, minute, weekday_name);
        }
    }
}

// 同时设置时间和星期 (支持多语言) - 从gif_player.cpp第1155-1178行移动
void setTimeAndWeekdayWithLang(uint8_t hour, uint8_t minute, uint8_t weekday, WeekdayLanguage language) {
    if (hour < 24 && minute < 60 && weekday <= 6) {
        time_info.hour = hour;
        time_info.minute = minute;
        time_info.weekday = weekday;

        // 更新全局语言状态
        current_display_language = language;

        // 绘制时间 (时间显示不受语言影响)
        drawTimeToRegion(&display_regions[REGION_TIME], hour, minute);

        // 绘制星期 (使用指定语言)
        drawWeekdayToRegionWithLang(&display_regions[REGION_WEEKDAY], weekday, language);

        // 输出调试信息 (根据语言选择格式)
        const char* weekday_name = getWeekdayNameWithLang(weekday, language);
        if (language == LANG_CHINESE) {
           printf("Time and weekday set to: %02d:%02d 星期%s\n", hour, minute, weekday_name);
        } else {
           printf("Time and weekday set to: %02d:%02d %s\n", hour, minute, weekday_name);
        }
    }
}

// 设置时间颜色 - 从gif_player.cpp第1181-1185行移动
void setTimeColor(uint16_t color) {
    time_info.time_color = color;
    display_regions[REGION_TIME].text_color = color;
    drawTimeToRegion(&display_regions[REGION_TIME], time_info.hour, time_info.minute);
}

// 设置星期颜色 - 从gif_player.cpp第1188-1193行移动
void setWeekdayColor(uint16_t color) {
    time_info.weekday_color = color;
    display_regions[REGION_WEEKDAY].text_color = color;
    // 使用当前全局语言设置重新绘制
    drawWeekdayToRegionWithLang(&display_regions[REGION_WEEKDAY], time_info.weekday, current_display_language);
}

// 设置星期 (使用当前全局语言设置，保持向后兼容) - 从gif_player.cpp第1196-1209行移动
void setWeekday(uint8_t weekday) {
    if (weekday <= 6) {
        time_info.weekday = weekday;
        // 使用当前全局语言设置进行显示
        drawWeekdayToRegionWithLang(&display_regions[REGION_WEEKDAY], weekday, current_display_language);

        const char* weekday_name = getWeekdayNameWithLang(weekday, current_display_language);
        if (current_display_language == LANG_CHINESE) {
            Serial.printf("Weekday updated: 星期%s\n", weekday_name);
        } else {
            Serial.printf("Weekday updated: %s\n", weekday_name);
        }
    }
}

// 设置星期 (支持多语言) - 从gif_player.cpp第1212-1228行移动
void setWeekdayWithLanguage(uint8_t weekday, WeekdayLanguage language) {
    if (weekday <= 6) {
        time_info.weekday = weekday;

        // 更新全局语言状态
        current_display_language = language;

        drawWeekdayToRegionWithLang(&display_regions[REGION_WEEKDAY], weekday, language);

        const char* weekday_name = getWeekdayNameWithLang(weekday, language);
        if (language == LANG_CHINESE) {
            Serial.printf("Weekday set to: 星期%s\n", weekday_name);
        } else {
            Serial.printf("Weekday set to: %s\n", weekday_name);
        }
    }
}

// 获取字符索引 - 从gif_player.cpp第1236-1240行移动
uint8_t getCharIndex(char c) {
    if (c == ':') return 0;
    if (c >= '0' && c <= '9') return c - '0' + 1;
    return 0;  // 默认返回冒号
}

// 获取星期名称 (中文) - 从gif_player.cpp第1243-1249行移动
const char* getWeekdayName(uint8_t weekday) {
    static const char* weekday_names[] = {"日", "一", "二", "三", "四", "五", "六"};
    if (weekday <= 6) {
        return weekday_names[weekday];
    }
    return "一";  // 默认返回周一
}

// 获取星期名称 (支持多语言) - 从gif_player.cpp第1252-1275行移动
const char* getWeekdayNameWithLang(uint8_t weekday, WeekdayLanguage language) {
    switch (language) {
        case LANG_CHINESE:
            {
                static const char* chinese_names[] = {"日", "一", "二", "三", "四", "五", "六"};
                if (weekday <= 6) {
                    return chinese_names[weekday];
                }
                return "一";
            }

        case LANG_ENGLISH:
            {
                static const char* english_names[] = {"SUN", "MON", "TUE", "WED", "THU", "FRI", "SAT"};
                if (weekday <= 6) {
                    return english_names[weekday];
                }
                return "MON";
            }

        default:
            return getWeekdayName(weekday);
    }
}

// 全局语言管理函数实现 - 从gif_player.cpp第1349-1366行移动
void setSystemDisplayLanguage(WeekdayLanguage language) {
    if (language < LANG_COUNT) {
        current_display_language = language;
        // 重新绘制当前星期显示以应用新语言
        updateWeekdayWithCurrentLanguage(time_info.weekday);
        Serial.printf("System display language changed to: %d\n", language);
    }
}

WeekdayLanguage getSystemDisplayLanguage() {
    return current_display_language;
}

void updateWeekdayWithCurrentLanguage(uint8_t weekday) {
    if (weekday <= 6) {
        drawWeekdayToRegionWithLang(&display_regions[REGION_WEEKDAY], weekday, current_display_language);
    }
}

// ==================== 大屏模式核心实现 ====================

// 更新区域可见性 - 从gif_player.cpp第1371-1385行移动
void updateRegionVisibility() {
    if (current_display_mode == DISPLAY_MODE_SMALL) {
        // 小屏模式：显示原有三分区
        display_regions[REGION_TIME].visible = true;
        display_regions[REGION_WEEKDAY].visible = true;
        display_regions[REGION_BIG_SCREEN].visible = false;
        printf("Region visibility: Small mode activated\n");
    } else {
        // 大屏模式：只显示大屏区域
        display_regions[REGION_TIME].visible = false;
        display_regions[REGION_WEEKDAY].visible = false;
        display_regions[REGION_BIG_SCREEN].visible = true;
        printf("Region visibility: Big mode activated\n");
    }
}

// 强制清除整个区域 - 从gif_player.cpp第1388-1400行移动
void forceClearRegion(DisplayRegion* region) {
    if (!region->visible || !dma_display) return;

    printf("Force clearing region (%d,%d) to (%d,%d)\n",
           region->x1, region->y1, region->x2, region->y2);

    // 逐像素清除整个区域
    for (int y = region->y1; y <= region->y2; y++) {
        for (int x = region->x1; x <= region->x2; x++) {
            dma_display->drawPixel(x, y, region->bg_color);
        }
    }
}

// 设置显示模式 - 从gif_player.cpp第1403-1441行移动
void setDisplayMode(DisplayMode mode) {
    if (mode != current_display_mode) {
        printf("Switching display mode from %s to %s\n",
               (current_display_mode == DISPLAY_MODE_SMALL) ? "SMALL" : "BIG",
               (mode == DISPLAY_MODE_SMALL) ? "SMALL" : "BIG");

        // 自动进入时钟模式（如果尚未激活且计分模式未激活）
        if (!clock_mode_active && !isScoreModeActive()) {
            printf("Auto-entering Clock Mode due to display mode change\n");
            clock_mode_active = true;
            enableTimeDisplayMode();
        }

        // 先强制清除相关区域
        if (mode == DISPLAY_MODE_BIG) {
            // 切换到大屏模式：清除小屏区域
            forceClearRegion(&display_regions[REGION_TIME]);
            forceClearRegion(&display_regions[REGION_WEEKDAY]);
        } else {
            // 切换到小屏模式：清除大屏区域
            forceClearRegion(&display_regions[REGION_BIG_SCREEN]);
        }

        current_display_mode = mode;
        printf("=== DISPLAY MODE SET === current_display_mode is now: %s\n",
               (current_display_mode == DISPLAY_MODE_SMALL) ? "SMALL" : "BIG");
        updateRegionVisibility();

        if (mode == DISPLAY_MODE_BIG) {
            // 重置大屏状态
            big_screen_state = BIG_SCREEN_TIME;
            big_screen_last_switch = millis();

            // === 新增：完整的大屏模式初始化 ===
            // 1. 确保切换间隔已设置（如果未设置则使用默认值）
            if (big_screen_switch_interval == 0) {
                big_screen_switch_interval = 5000;  // 默认5秒
                printf("Auto-set big screen switch interval to: 5000ms\n");
            }

            // 2. 设置初始切换时机 - 立即触发第一次切换
            big_screen_last_switch = millis() - big_screen_switch_interval + 1000;  // 1秒后触发第一次切换
            printf("Big screen first switch scheduled in: 1000ms\n");

            // 3. 确保语言设置有效
            if (big_screen_language >= LANG_COUNT) {
                big_screen_language = LANG_ENGLISH;  // 默认英文
                printf("Auto-set big screen language to: ENGLISH\n");
            }

            // 4. 打印完整的初始化状态
            printf("=== Big Screen Mode Initialized ===\n");
            printf("Switch interval: %lu ms\n", big_screen_switch_interval);
            printf("Initial state: %s\n", (big_screen_state == BIG_SCREEN_TIME) ? "TIME" : "WEEKDAY");
            printf("Language: %s\n", (big_screen_language == LANG_CHINESE) ? "CHINESE" : "ENGLISH");
            printf("First switch in: %lu ms\n", big_screen_switch_interval - (millis() - big_screen_last_switch));

            // 同步时间信息（确保大屏显示最新时间）
            printf("Big screen mode activated, current time: %02d:%02d, weekday: %d\n",
                   time_info.hour, time_info.minute, time_info.weekday);

            // 强制重绘（清除静态缓存）
            drawBigScreenTime(true);  // 模式切换时强制重绘
            printf("Display mode changed to BIG\n");
        } else {
            // 恢复小屏显示
            drawTimeToRegion(&display_regions[REGION_TIME], time_info.hour, time_info.minute);
            drawWeekdayToRegionWithLang(&display_regions[REGION_WEEKDAY], time_info.weekday, current_display_language);
            printf("Display mode changed to SMALL\n");
        }
    }
}

// 更新大屏显示 - 从gif_player.cpp第1444-1544行移动
void updateBigScreenDisplay() {
    static unsigned long last_debug_time = 0;

    // 每5秒输出一次基本调试信息
    if (millis() - last_debug_time > 5000) {
        printf("=== Big Screen Update === millis: %lu, mode: %s\n",
               millis(), (current_display_mode == DISPLAY_MODE_BIG) ? "BIG" : "SMALL");
        last_debug_time = millis();
    }

    if (current_display_mode != DISPLAY_MODE_BIG) {
        if (millis() - last_debug_time > 2000) {
            printf("Not in BIG mode, returning\n");
        }
        return;
    }

    unsigned long now = millis();
    static unsigned long last_time_update = 0;
    static uint8_t last_minute = 255; // 初始化为无效值，确保首次更新
    static uint8_t last_weekday = 255;

    // 添加时间间隔检查的调试信息
    if (millis() - last_debug_time <= 100) { // 只在调试输出时显示
        printf("Time check: now=%lu, last_switch=%lu, interval=%lu\n",
               now, big_screen_last_switch, big_screen_switch_interval);
        printf("Time diff: %lu, need: %lu\n",
               now - big_screen_last_switch, big_screen_switch_interval);
        printf("Current state: %s\n",
               (big_screen_state == BIG_SCREEN_TIME) ? "TIME" : "WEEKDAY");
    }

    // 每秒检查一次时间更新（与小屏模式保持一致）
    if (now - last_time_update >= 1000) {
        // 使用独立的秒计数器（不修改原有结构体）
        static uint8_t big_screen_seconds = 0;

        big_screen_seconds++;
        if (big_screen_seconds >= 60) {
            big_screen_seconds = 0;
            time_info.minute++;
            if (time_info.minute >= 60) {
                time_info.minute = 0;
                time_info.hour++;
                if (time_info.hour >= 24) {
                    time_info.hour = 0;
                    // 新的一天，更新星期
                    time_info.weekday = (time_info.weekday + 1) % 7;
                    printf("New day! Weekday changed to: %d\n", time_info.weekday);
                }
                printf("Time updated: %02d:%02d\n", time_info.hour, time_info.minute);
            }
        }
        last_time_update = now;
    }

    // 检查是否需要重新绘制（时间或星期发生变化）
    bool need_redraw = false;
    if (time_info.minute != last_minute) {
        last_minute = time_info.minute;
        need_redraw = true;
        printf("Minute changed, need redraw\n");
    }
    if (time_info.weekday != last_weekday) {
        last_weekday = time_info.weekday;
        need_redraw = true;
        printf("Weekday changed, need redraw\n");
    }

    // 检查是否需要切换显示内容（时间 <-> 星期）
    bool state_changed = false;
    if (now - big_screen_last_switch >= big_screen_switch_interval) {
        BigScreenState old_state = big_screen_state;
        big_screen_state = (big_screen_state == BIG_SCREEN_TIME) ?
                          BIG_SCREEN_WEEKDAY : BIG_SCREEN_TIME;
        big_screen_last_switch = now;
        state_changed = true;

        printf("*** STATE SWITCH TRIGGERED ***\n");
        printf("Switch from %s to %s\n",
               (old_state == BIG_SCREEN_TIME) ? "TIME" : "WEEKDAY",
               (big_screen_state == BIG_SCREEN_TIME) ? "TIME" : "WEEKDAY");
        printf("New last_switch time: %lu\n", big_screen_last_switch);
        printf("Next switch in: %lu ms\n", big_screen_switch_interval);
    } else {
        // 添加倒计时调试信息
        unsigned long remaining = big_screen_switch_interval - (now - big_screen_last_switch);
        if (millis() - last_debug_time <= 100) {
            printf("No switch needed, remaining: %lu ms\n", remaining);
        }
    }

    // 只在需要时重新绘制（避免闪烁）
    if (need_redraw || state_changed) {
        if (big_screen_state == BIG_SCREEN_TIME) {
            drawBigScreenTime(state_changed);  // 状态切换时强制重绘
        } else {
            drawBigScreenWeekday(state_changed);  // 状态切换时强制重绘
        }
    }
}

// 绘制大屏时间（8x32数字字体，修正布局）- 从gif_player.cpp第1547-1606行移动
void drawBigScreenTime(bool force_redraw) {
    DisplayRegion* region = &display_regions[REGION_BIG_SCREEN];
    static uint8_t last_drawn_hour = 255;
    static uint8_t last_drawn_minute = 255;

    // 重绘条件：时间变化 OR 强制重绘（状态切换）
    bool time_changed = (time_info.hour != last_drawn_hour || time_info.minute != last_drawn_minute);
    bool need_redraw = time_changed || force_redraw;

    if (need_redraw) {
       
     forceClearRegion(region->x1, region->y1, region->x2, region->y2);

        last_drawn_hour = time_info.hour;
        last_drawn_minute = time_info.minute;

        if (time_changed) {
            printf("Big screen time redraw: %02d:%02d (time changed)\n", time_info.hour, time_info.minute);
        } else if (force_redraw) {
            printf("Big screen time redraw: %02d:%02d (state switch forced)\n", time_info.hour, time_info.minute);
        }
    } else {
        // 时间没有变化且非强制重绘，不需要重绘
        printf("Big screen time: no redraw needed\n");
        return;
    }

    // 设置时间颜色
    region->text_color = big_screen_time_color;
    region->bg_color = big_screen_bg_color;

    int region_width = region->x2 - region->x1 + 1;  // 48像素
    int region_height = region->y2 - region->y1 + 1; // 32像素

    printf("Drawing big screen time in region %dx%d, region bounds: (%d,%d)-(%d,%d)\n",
           region_width, region_height, region->x1, region->y1, region->x2, region->y2);

    // 8x32字符，48像素宽度可以放6个字符（8×6=48）
    // 显示完整时间 HH:MM（5个字符：2+1+2）
    int char_width = 8;
    int char_height = 32;
    int total_width = 5 * char_width; // 40像素

    // 在48像素宽度内居中显示40像素的内容
    int start_x = (region_width - total_width) / 2; // 居中：(48-40)/2 = 4
    int start_y = 0; // 32像素高度正好匹配字符高度

    printf("Time layout: 5 chars × 8px = 40px total, centered at x=%d\n", start_x);

    // 绘制完整时间 HH:MM
    drawBig32x8TimeChar(region, time_info.hour / 10 + 1, start_x + 0 * char_width, start_y);  // 小时十位
    drawBig32x8TimeChar(region, time_info.hour % 10 + 1, start_x + 1 * char_width, start_y);  // 小时个位
    drawBig32x8TimeChar(region, 0, start_x + 2 * char_width, start_y);                        // 冒号
    drawBig32x8TimeChar(region, time_info.minute / 10 + 1, start_x + 3 * char_width, start_y); // 分钟十位
    drawBig32x8TimeChar(region, time_info.minute % 10 + 1, start_x + 4 * char_width, start_y); // 分钟个位

    printf("Big screen time drawn (full): %02d:%02d at offset (%d,%d)\n",
           time_info.hour, time_info.minute, start_x, start_y);
}

// 绘制大屏星期（32x16字体）- 从gif_player.cpp第1609-1688行移动
void drawBigScreenWeekday(bool force_redraw) {
    DisplayRegion* region = &display_regions[REGION_BIG_SCREEN];
    static uint8_t last_drawn_weekday = 255;
    static WeekdayLanguage last_drawn_language = (WeekdayLanguage)255;

    // 重绘条件：星期/语言变化 OR 强制重绘（状态切换）
    bool content_changed = (time_info.weekday != last_drawn_weekday || big_screen_language != last_drawn_language);
    bool need_redraw = content_changed || force_redraw;

    if (need_redraw) 
    {
        forceClearRegion(region->x1, region->y1, region->x2, region->y2);
        last_drawn_weekday = time_info.weekday;
        last_drawn_language = big_screen_language;

        if (content_changed) 
        {
            printf("Big screen weekday redraw: %d, lang: %s (content changed)\n",
                   time_info.weekday, (big_screen_language == LANG_CHINESE) ? "CN" : "EN");
        } 
        else if (force_redraw) 
        {
            printf("Big screen weekday redraw: %d, lang: %s (state switch forced)\n",
                   time_info.weekday, (big_screen_language == LANG_CHINESE) ? "CN" : "EN");
        }
    
    } 
    else {
        // 星期和语言都没有变化且非强制重绘，不需要重绘
        printf("Big screen weekday: no redraw needed\n");
        return;
    }

    // 设置星期颜色
    region->text_color = big_screen_weekday_color;
    region->bg_color = big_screen_bg_color;

    int region_width = region->x2 - region->x1 + 1;  // 48像素
    int region_height = region->y2 - region->y1 + 1; // 32像素

    if (big_screen_language == LANG_CHINESE) {
        // 中文显示：显示完整"星期X"（3个字符）
        int char_width = 16;  // 修正：每个字符16宽×32高
        int char_height = 32;
        int total_chars = 3;  // "星期X"三个字符
        int total_width = total_chars * char_width; // 48像素

        // 48像素宽度正好可以放3个16像素字符
        int start_x = 0; // 左对齐，正好填满48像素
        int start_y = 0; // 顶对齐，32像素高度正好匹配

        printf("Chinese layout: 3 chars × 16px = 48px total, at (%d,%d) in %dx%d region\n",
               start_x, start_y, region_width, region_height);

        // 显示完整"星期X"
        drawBig32x16ChineseChar(region, 0, start_x + 0 * char_width, start_y); // "星"
        drawBig32x16ChineseChar(region, 1, start_x + 1 * char_width, start_y); // "期"
        drawBig32x16ChineseChar(region, time_info.weekday + 2, start_x + 2 * char_width, start_y); // 数字部分

        const char* weekday_names[] = {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};
        printf("Big screen weekday drawn (Chinese): %s at offset (%d,%d)\n",
               weekday_names[time_info.weekday], start_x, start_y);
    } else {
        // 英文显示：显示完整星期前三个字母（如"SAT"）
        int char_width = 16; // 英文字符宽度16像素
        int char_height = 32;
        int total_chars = 3; // 显示3个字符
        int total_width = total_chars * char_width; // 48像素

        // 48像素宽度正好可以放3个16像素字符
        int start_x = 0; // 左对齐，正好填满48像素
        int start_y = 0; // 顶对齐，32像素高度正好匹配

        printf("English layout: 3 chars × 16px = 48px total, at (%d,%d) in %dx%d region\n",
               start_x, start_y, region_width, region_height);

        drawBig32x16EnglishChars(region, time_info.weekday, start_x, start_y);

        const char* weekday_names[] = {"SUNDAY", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY"};
        printf("Big screen weekday drawn (English): %s at offset (%d,%d)\n",
               weekday_names[time_info.weekday], start_x, start_y);
    }
}

// 绘制8x32数字字符（修正格式：8宽×32高）- 从gif_player.cpp第1691-1723行移动
void drawBig32x8TimeChar(DisplayRegion* region, uint8_t char_index, int x, int y) {
    if (!region->visible || !dma_display || char_index >= 11) return;

    const uint8_t* char_data = number_font[char_index];

    printf("Drawing time char[%d] at (%d,%d) - format: 8w×32h\n", char_index, x, y);

    // 数字字体格式：8宽×32高，每字节代表一行的8个像素
    // number_font[11][32]：每个字符32字节，每字节是一行
    for (int row = 0; row < 32; row++) {
        uint8_t row_data = pgm_read_byte(&char_data[row]); // 从Flash读取

        for (int bit = 0; bit < 8; bit++) {
            int pixel_x = region->x1 + x + bit;
            int pixel_y = region->y1 + y + row;

            // 边界检查
            if (pixel_x > region->x2 || pixel_y > region->y2 ||
                pixel_x < region->x1 || pixel_y < region->y1) {
                printf("Pixel (%d,%d) out of bounds [%d,%d]-[%d,%d]\n",
                       pixel_x, pixel_y, region->x1, region->y1, region->x2, region->y2);
                continue;
            }

            if (row_data & (0x01 << bit)) {
                dma_display->drawPixel(pixel_x, pixel_y, region->text_color);
            }
            // 删除auto_clear分支，不再绘制背景像素
        }
    }

    printf("Time char[%d] drawn: 8×32 pixels at region offset (%d,%d)\n", char_index, x, y);
}

// 绘制32高×16宽中文字符（正确格式）- 从gif_player.cpp第1757-1808行移动
void drawBig32x16ChineseChar(DisplayRegion* region, uint8_t char_index, int x, int y) {
    if (!region->visible || !dma_display || char_index >= 9) return;

    const uint8_t* char_data = font_chinese[char_index];

    printf("Drawing Chinese char[%d] at (%d,%d) - format: 32h×16w\n", char_index, x, y);

    // 中文字体数据：font_chinese[9][72]
    // 格式：32高×16宽，每个字符72字节
    // 数据结构：32行，每行2字节（16位宽度），共64字节，剩余8字节可能是额外信息
    for (int row = 0; row < 32; row++) {
        // 每行2字节（16位宽度）
        int data_index = row * 2;
        if (data_index + 1 >= 72) break; // 安全检查，防止越界

        uint8_t left_byte = pgm_read_byte(&char_data[data_index]);     // 从Flash读取左半部分
        uint8_t right_byte = pgm_read_byte(&char_data[data_index + 1]); // 从Flash读取右半部分

        // 绘制左半部分（8位）
        for (int bit = 0; bit < 8; bit++) {
            int pixel_x = region->x1 + x + bit;
            int pixel_y = region->y1 + y + row;

            // 边界检查
            if (pixel_x > region->x2 || pixel_y > region->y2 ||
                pixel_x < region->x1 || pixel_y < region->y1) continue;

            if (left_byte & (0x01 << bit)) {
                dma_display->drawPixel(pixel_x, pixel_y, region->text_color);
            }
            // 删除auto_clear分支，不再绘制背景像素
        }

        // 绘制右半部分（8位）
        for (int bit = 0; bit < 8; bit++) {
            int pixel_x = region->x1 + x + 8 + bit;
            int pixel_y = region->y1 + y + row;

            // 边界检查
            if (pixel_x > region->x2 || pixel_y > region->y2 ||
                pixel_x < region->x1 || pixel_y < region->y1) continue;

            if (right_byte & (0x01 << bit)) {
                dma_display->drawPixel(pixel_x, pixel_y, region->text_color);
            }
            // 删除auto_clear分支，不再绘制背景像素
        }
    }

    printf("Chinese char[%d] drawn: 32h×16w pixels at region offset (%d,%d)\n",
           char_index, x, y);
}

// 绘制32高×16宽英文字符（正确格式）- 从gif_player.cpp第1811-1886行移动
void drawBig32x16EnglishChars(DisplayRegion* region, uint8_t weekday, int x, int y) {
    if (!region->visible || !dma_display || weekday > 6) return;

    printf("Drawing English weekday[%d] at (%d,%d) - format: 32h×16w\n", weekday, x, y);

    const char* weekday_names[] = {"SUNDAY", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY"};

    // 字符索引映射：每个星期对应3个字符
    // SUN: 0,1,2   MON: 3,4,5   TUE: 6,7,8   WED: 9,10,11   THU: 12,13,14   FRI: 15,16,17   SAT: 18,19,20
    uint8_t base_index = weekday * 3; // 每个星期3个字符

    printf("Drawing weekday %s (index %d), base_index=%d\n", weekday_names[weekday], weekday, base_index);

    // 绘制3个字符（前三个字母）
    for (int char_pos = 0; char_pos < 3; char_pos++) {
        uint8_t char_index = base_index + char_pos;

        if (char_index >= 21) break; // 安全检查

        // 计算字符位置：每个字符16像素宽，但区域只有48像素
        // 3个字符需要48像素，正好填满
        int char_x = x + char_pos * 16;

        printf("Drawing char[%d] at x=%d\n", char_index, char_x);
        const uint8_t* char_data = en_font[char_index];

        printf("Drawing char_index[%d] at char_x=%d, region bounds: (%d,%d)-(%d,%d)\n",
               char_index, char_x, region->x1, region->y1, region->x2, region->y2);

        // 英文字体格式：32高×16宽
        // en_font[21][64]：每个字符64字节
        // 正确的数据结构：32行，每行2字节（16位宽度）
        // 32行 × 2字节/行 = 64字节

        // 按照正确的32高×16宽格式绘制
        for (int row = 0; row < 32; row++) {
            // 数据存储方式：每行2字节连续存储
            uint8_t left_byte = pgm_read_byte(&char_data[row * 2]);     // 左半部分（8位）
            uint8_t right_byte = pgm_read_byte(&char_data[row * 2 + 1]); // 右半部分（8位）

            // 绘制左半部分（8位）
            for (int bit = 0; bit < 8; bit++) {
                int pixel_x = region->x1 + char_x + bit;
                int pixel_y = region->y1 + y + row;

                // 边界检查
                if (pixel_x > region->x2 || pixel_y > region->y2 ||
                    pixel_x < region->x1 || pixel_y < region->y1) continue;

                if (left_byte & (0x01 << bit)) {
                  dma_display->drawPixel(pixel_x, pixel_y, region->text_color);
                }
                // 删除auto_clear分支，不再绘制背景像素
            }

            // 绘制右半部分（8位）
            for (int bit = 0; bit < 8; bit++) {
                int pixel_x = region->x1 + char_x + 8 + bit;
                int pixel_y = region->y1 + y + row;

                // 边界检查
                if (pixel_x > region->x2 || pixel_y > region->y2 ||
                    pixel_x < region->x1 || pixel_y < region->y1) continue;

                if (right_byte & (0x01 << bit)) {
                    dma_display->drawPixel(pixel_x, pixel_y, region->text_color);
                }
                // 删除auto_clear分支，不再绘制背景像素
            }
        }

        printf("English char[%d] drawn: 32h×16w pixels at char_x=%d\n", char_index, char_x);
    }

    printf("English weekday drawn: %s (3 chars)\n", weekday_names[weekday]);
}

// ==================== 大屏配置函数 ====================

// 设置大屏切换间隔 - 从gif_player.cpp第1891-1894行移动
void setBigScreenSwitchInterval(unsigned long interval_ms) {
    big_screen_switch_interval = interval_ms;
    printf("Big screen switch interval set to: %lu ms\n", interval_ms);
}

// 设置大屏语言 - 从gif_player.cpp第1897-1907行移动
void setBigScreenLanguage(WeekdayLanguage language) {
    if (language < LANG_COUNT && language != big_screen_language) {
        big_screen_language = language;
        printf("Big screen language changed to: %d\n", language);

        // 只有在大屏模式且当前显示星期时才重绘
        if (current_display_mode == DISPLAY_MODE_BIG && big_screen_state == BIG_SCREEN_WEEKDAY) {
            drawBigScreenWeekday(true);  // 语言切换时强制重绘
        }
    }
}

// 设置大屏时间和星期 - 从gif_player.cpp第1910-1924行移动
void setBigScreenTimeAndWeekday(uint8_t hour, uint8_t minute, uint8_t weekday, WeekdayLanguage language) {
    if (hour < 24 && minute < 60 && weekday <= 6 && language < LANG_COUNT) {
        time_info.hour = hour;
        time_info.minute = minute;
        time_info.weekday = weekday;
        big_screen_language = language;

        if (current_display_mode == DISPLAY_MODE_BIG) {
            updateBigScreenDisplay();
        }

        printf("Big screen time and weekday set: %02d:%02d, weekday=%d, lang=%d\n",
               hour, minute, weekday, language);
    }
}

// ==================== 大屏颜色控制函数 ====================

// 设置大屏时间颜色 - 从gif_player.cpp第1929-1935行移动
// void setBigScreenTimeColor(uint16_t color) {
//     big_screen_time_color = color;
//     if (current_display_mode == DISPLAY_MODE_BIG && big_screen_state == BIG_SCREEN_TIME) {
//         updateBigScreenDisplay();
//     }
//     printf("Big screen time color set to: 0x%04X\n", color);
// }

// // 设置大屏星期颜色 - 从gif_player.cpp第1938-1944行移动
// void setBigScreenWeekdayColor(uint16_t color) {
//     big_screen_weekday_color = color;
//     if (current_display_mode == DISPLAY_MODE_BIG && big_screen_state == BIG_SCREEN_WEEKDAY) {
//         updateBigScreenDisplay();
//     }
//     printf("Big screen weekday color set to: 0x%04X\n", color);
// }
void setBigScreenTimeColor(uint16_t color) {
    big_screen_time_color = color;
    if (current_display_mode == DISPLAY_MODE_BIG) {  // 只检查显示模式
        updateBigScreenDisplay();  // 总是触发重绘
    }
    printf("Big screen time color set to: 0x%04X\n", color);
}

void setBigScreenWeekdayColor(uint16_t color) {
    big_screen_weekday_color = color;
    if (current_display_mode == DISPLAY_MODE_BIG) {  // 只检查显示模式
        updateBigScreenDisplay();  // 总是触发重绘
    }
    printf("Big screen weekday color set to: 0x%04X\n", color);
}

// 设置大屏背景颜色 - 从gif_player.cpp第1947-1954行移动
void setBigScreenBackgroundColor(uint16_t color) {
    big_screen_bg_color = color;
    display_regions[REGION_BIG_SCREEN].bg_color = color;
    if (current_display_mode == DISPLAY_MODE_BIG) {
        updateBigScreenDisplay();
    }
    printf("Big screen background color set to: 0x%04X\n", color);
}

// 快速设置大屏颜色主题 - 从gif_player.cpp第1985-1990行移动
void setBigScreenColorTheme(uint16_t time_color, uint16_t weekday_color, uint16_t bg_color) {
    setBigScreenTimeColor(time_color);
    setBigScreenWeekdayColor(weekday_color);
    setBigScreenBackgroundColor(bg_color);
    printf("Big screen color theme applied\n");
}

// ==================== 测试函数 ====================

// 启动大屏模式循环演示 - 从gif_player.cpp第1995-2063行移动
void testBigScreenMode(uint8_t hour, uint8_t minute, uint8_t weekday, WeekdayLanguage language) {
    printf("\n==================== BIG SCREEN MODE DEMO STARTED ====================\n");
    printf("Demo parameters: %02d:%02d, weekday=%d, language=%s\n",
           hour, minute, weekday, (language == LANG_CHINESE) ? "CHINESE" : "ENGLISH");

    // 设置演示参数
    time_info.hour = hour;
    time_info.minute = minute;
    time_info.weekday = weekday;
    setBigScreenLanguage(language);

    printf("Current mode: %s\n", (current_display_mode == DISPLAY_MODE_SMALL) ? "SMALL" : "BIG");
    printf("Free heap: %d bytes\n", ESP.getFreeHeap());

    // 修复：先设置参数，再切换模式
    printf("\nSetting up big screen parameters...\n");

    // 1. 先设置切换间隔
    setBigScreenSwitchInterval(5000);
    printf("Switch interval set to: 5 seconds\n");

    // 2. 设置初始状态和时间（强制立即触发切换）
    big_screen_state = BIG_SCREEN_TIME; // 从时间显示开始
    big_screen_last_switch = millis() - 5000; // 设置为5秒前，立即触发切换
    printf("Initial state: TIME, last_switch set to trigger immediate switch\n");
    printf("Current millis: %lu, last_switch: %lu\n", millis(), big_screen_last_switch);

    // 3. 最后切换到大屏模式
    printf("\nSwitching to big screen mode...\n");
    setDisplayMode(DISPLAY_MODE_BIG);
    printf("Mode switched to: %s\n", (current_display_mode == DISPLAY_MODE_BIG) ? "BIG" : "SMALL");

    // 启动大屏模式（让系统自动循环切换）
    printf("\nStarting big screen demo mode...\n");
    printf("Will automatically switch between TIME and WEEKDAY every 5 seconds\n");
    printf("Demo will run continuously - system will handle automatic switching\n");

    printf("Starting with TIME display...\n");

    // 不使用delay()，不阻塞主循环
    // 让updateBigScreenDisplay()自动处理切换
    printf("\n==================== DEMO RUNNING ====================\n");
    printf("Big screen demo is now running!\n");
    printf("- TIME display: %02d:%02d\n", time_info.hour, time_info.minute);
    printf("- WEEKDAY display: %s\n", (language == LANG_CHINESE) ? "中文星期" : "English weekday");
    printf("- Auto-switching every 5 seconds\n");
    printf("- Time will auto-update every minute\n");
    printf("Demo will continue until manually stopped...\n");
}

// ==================== 系统初始化函数 ====================

// 初始化时间系统 - 新增函数，整合时间系统初始化
void initTimeSystem() {
    initDisplayRegions();
    setupTimeRegions();
}

// 更新区域 - 新增函数，提供基本的区域更新功能
void updateRegion(DisplayRegion* region) {
    // 基本的区域更新逻辑，可以根据需要扩展
    if (!region || !region->visible) return;

    // 这里可以添加区域更新的具体逻辑
    // 目前保持简单实现
}

// // 在区域内绘制位图 - 新增函数，提供位图绘制功能
// void drawBitmapToRegion(DisplayRegion* region, const uint8_t* bitmap,
//                        uint8_t width, uint8_t height, int8_t offset_x, int8_t offset_y) {
//     if (!region || !region->visible || !bitmap || !dma_display) return;

//     for (uint8_t y = 0; y < height; y++) {
//         for (uint8_t x = 0; x < width; x++) {
//             int pixel_x = region->x1 + offset_x + x;
//             int pixel_y = region->y1 + offset_y + y;

//             // 边界检查
//             if (pixel_x > region->x2 || pixel_y > region->y2 ||
//                 pixel_x < region->x1 || pixel_y < region->y1) continue;

//             // 简单的位图绘制逻辑（假设位图是单色的）
//             uint8_t byte_index = (y * ((width + 7) / 8)) + (x / 8);
//             uint8_t bit_index = x % 8;

//             if (bitmap[byte_index] & (0x80 >> bit_index)) {
//                 dma_display->drawPixel(pixel_x, pixel_y, region->text_color);
//             }
//         }
//     }
// }

//*********************GIF变换函数*************************/
// bool changeStartupGIF(uint8_t gifnum)
// {
//     // 构建完整路径
//      char filename[20];
//      snprintf(filename, sizeof(filename), "%d.gif", gifnum);

//     String fullPath = String(GIF_STORAGE_PATH) + filename;

//    printf("Attempting to change startup GIF to: %s\n", fullPath.c_str());

//     // 检查文件是否存在
//     if (!LittleFS.exists(fullPath)) {
//         printf("Startup GIF not found: %s\n", fullPath.c_str());
//         return false;
//     }

//     // 检查当前内存状态
//     printf("Free heap before change: %d bytes\n", ESP.getFreeHeap());

//     // 停止当前播放的GIF
//     stopGIF();
//     delay(100);  // 确保清理完成

//     printf("Free heap after cleanup: %d bytes\n", ESP.getFreeHeap());

//     // 播放新的开机动画
//     if (playGIF(fullPath.c_str())) {
//         printf("Startup animation successfully changed to: %s\n", filename);
//         return true;
//     } else {
//         printf("Failed to play startup GIF: %s\n", filename);
//         return false;
//     }
// }


// ==================== 时钟模式状态管理函数 ====================

// 进入时钟模式，自动退出计分模式
void enterClockMode() {
    if (clock_mode_active) {
        printf("Already in Clock Mode\n");
        return;
    }

    printf("Entering Clock Mode...\n");

    // 互斥检查：如果计分模式激活，先退出计分模式
    if (isScoreModeActive()) {
        printf("Score Mode is active, exiting Score Mode first...\n");
        exitScoreMode();
    }

    // 激活时钟模式
    clock_mode_active = true;

    // 确保时间显示启用
    enableTimeDisplayMode();

    // 初始化时钟显示系统（避免在setDisplayMode中重复调用）
    if (!display_regions[REGION_TIME].visible && !display_regions[REGION_BIG_SCREEN].visible) {
        initDisplayRegions();
        setupTimeRegions();
    }

    printf("Clock Mode entered successfully\n");
}

// 退出时钟模式
void exitClockMode() {
    if (!clock_mode_active) {
        printf("Not in Clock Mode\n");
        return;
    }

    printf("Exiting Clock Mode...\n");
    clock_mode_active = false;

    // 清除时钟显示区域
    if (current_display_mode == DISPLAY_MODE_SMALL) {
        clearRegion(&display_regions[REGION_TIME]);
        clearRegion(&display_regions[REGION_WEEKDAY]);
    } else {
        clearRegion(&display_regions[REGION_BIG_SCREEN]);
    }

    printf("Clock Mode exited successfully\n");
}

// 检查时钟模式状态
bool isClockModeActive() {
    return clock_mode_active;
}

// ==================== 蓝牙控制接口实现 ====================

// 处理蓝牙时钟模式命令
void handleBluetoothClockMode(uint8_t screenMode, uint8_t gifSelect,
                             uint8_t weekdayR, uint8_t weekdayG, uint8_t weekdayB,
                             uint8_t timeR, uint8_t timeG, uint8_t timeB,
                             uint8_t hour, uint8_t minute, uint8_t weekday, uint8_t language) {

    printf("Bluetooth Clock Mode Command Received:\n");
    printf("  Screen Mode: %s\n", (screenMode == 0) ? "Small" : "Big");
    printf("  GIF Select: %d (reserved)\n", gifSelect);
    printf("  Weekday Color: RGB(%d,%d,%d)\n", weekdayR, weekdayG, weekdayB);
    printf("  Time Color: RGB(%d,%d,%d)\n", timeR, timeG, timeB);
    printf("  Time: %02d:%02d\n", hour, minute);
    printf("  Weekday: %d\n", weekday);
    printf("  Language: %s\n", (language == 0) ? "Chinese" : "English");

    // 参数验证 - 严格按照协议要求的十六进制范围
    if (hour > 0x17) {  // 0x17 = 23
        printf("Error: Invalid hour 0x%02X (0x00~0x17 for 0~23)\n", hour);
        return;
    }
    if (minute > 0x3B) {  // 0x3B = 59
        printf("Error: Invalid minute 0x%02X (0x00~0x3B for 0~59)\n", minute);
        return;
    }
    if (weekday > 0x06) {  // 0x06 = 6
        printf("Error: Invalid weekday 0x%02X (0x00~0x06 for Sunday~Saturday)\n", weekday);
        return;
    }
    if (language > 0x01) {  // 0x01 = 1
        printf("Error: Invalid language 0x%02X (0x00=Chinese, 0x01=English)\n", language);
        return;
    }
    if (screenMode > 0x01) {  // 0x01 = 1
        printf("Error: Invalid screen mode 0x%02X (0x00=Small, 0x01=Big)\n", screenMode);
        return;
    }
    if (gifSelect > 0x07) {  // 0x07 = 7
        printf("Error: Invalid GIF select 0x%02X (0x00~0x07)\n", gifSelect);
        return;
    }

    // 进入时钟模式
    enterClockMode();

    // 设置显示模式
    DisplayMode targetMode = (screenMode == 0) ? DISPLAY_MODE_SMALL : DISPLAY_MODE_BIG;
    setDisplayMode(targetMode);

    // 设置语言
    WeekdayLanguage targetLang = (language == 0) ? LANG_CHINESE : LANG_ENGLISH;
    setSystemDisplayLanguage(targetLang);

    // 转换RGB888到RGB565颜色格式
    uint16_t weekdayColor = ((weekdayR & 0xF8) << 8) | ((weekdayG & 0xFC) << 3) | (weekdayB >> 3);
    uint16_t timeColor = ((timeR & 0xF8) << 8) | ((timeG & 0xFC) << 3) | (timeB >> 3);

    // // 设置颜色
    // setTimeColor(timeColor);
    // setWeekdayColor(weekdayColor);

    // // 设置时间和星期
    if (targetMode == DISPLAY_MODE_SMALL) {
        setTimeAndWeekdayWithLang(hour, minute, weekday, targetLang);
    } else {
        setBigScreenTimeAndWeekday(hour, minute, weekday, targetLang);
    }
    // 2. 先设置颜色（在切换模式之前）
    if (targetMode == DISPLAY_MODE_BIG) {
        setBigScreenTimeColor(timeColor);
        setBigScreenWeekdayColor(weekdayColor);
    }

    // 3. 再切换显示模式（这样绘制时就使用新颜色）
    setDisplayMode(targetMode);

    // 4. 小屏模式的颜色设置保持在后面
    if (targetMode == DISPLAY_MODE_SMALL) {
    setTimeColor(timeColor);
    setWeekdayColor(weekdayColor);
    }

    // GIF选择功能预留（暂时只打印日志）
    printf("GIF selection %d is reserved for future implementation\n", gifSelect);
    setGifDisplayOffset(0, 0);
     changeStartupGIF(gifSelect);
    printf("Bluetooth Clock Mode setup completed successfully\n");
}
