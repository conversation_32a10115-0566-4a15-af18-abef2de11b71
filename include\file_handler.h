#ifndef FILE_HANDLER_H
#define FILE_HANDLER_H

#include "BluetoothSerial.h"
#include "bluetooth_protocol.h"
#include "LittleFS.h"
#include "config.h"
#include "file_handler.h"

// 文件传输相关变量声明
extern bool fileTransferActive; // 文件传输是否处于活动状态
extern String currentFileName;
extern File currentFile;
extern uint32_t expectedFileSize;
extern uint32_t receivedFileSize;
extern unsigned long fileTransferStartTime;
extern unsigned long fileTransferTime_Every;

// 文件传输相关函数声明
void handleFileStartCommand(const BluetoothFrame &frame);
void handleFileDataCommand(const BluetoothFrame &frame);
void handleFileEndCommand(const BluetoothFrame &frame);
void handleFileListCommand(const BluetoothFrame &frame);
void handleFileDeleteCommand(const BluetoothFrame &frame);
void handlePlayGifCommand(const BluetoothFrame &frame);
void sendResponse(uint8_t command, const String &message);
void sendFileList();
void abortFileTransfer();
bool createGifsDirectory();
bool initLittleFS();
bool check_packetloss();

// 外部蓝牙串口对象引用
extern BluetoothSerial SerialBT;

// 外部GIF播放函数引用
extern bool playGIF(const char* filename);
extern bool playGIFAuto(const char* filename);

#endif // FILE_HANDLER_H
