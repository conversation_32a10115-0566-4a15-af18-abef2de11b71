#ifndef BLUETOOTH_PROTOCOL_H
#define BLUETOOTH_PROTOCOL_H

#include "Arduino.h"
#include "config.h"

extern uint32_t fileTransferTime_packetloss;
extern loopstate currentloopstate;

enum class ParseState
{
    WAITING_HEADER1,
    WAITING_HEADER2,
    WAITING_COMMAND,
    WAITING_LENGTH_HIGH,
    WAITING_LENGTH_LOW,
    WAITING_DATA,
    WAITING_TAIL1,
    WAITING_TAIL2,
    FRAME_COMPLETE
};

enum class ParseResult
{
    NEED_MORE_DATA,
    FRAME_COMPLETE,
    FRAME_ERROR,
    INVALID_COMMAND,
    DATA_TOO_LONG
};

struct BluetoothFrame
{
    uint8_t command;
    uint16_t dataLength;
    uint8_t *data; // 拥有自己的数据副本
    bool isValid;
    uint32_t timestamp;              // 接收时间戳
    mutable uint16_t *convertedData; // 缓存转换后的16位数据
    mutable bool isConverted;        // 转换标志
    bool ownsData;                   // 标记是否拥有数据内存

    BluetoothFrame() : command(0), dataLength(0), data(nullptr), isValid(false), timestamp(0), convertedData(nullptr), isConverted(false), ownsData(false) {}

    // 拷贝构造函数
    BluetoothFrame(const BluetoothFrame &other) : command(other.command), dataLength(other.dataLength),
                                                  isValid(other.isValid), timestamp(other.timestamp), convertedData(nullptr), isConverted(false), ownsData(false)
    {
        if (other.data && other.dataLength > 0)
        {
            data = new uint8_t[other.dataLength];
            memcpy(data, other.data, other.dataLength);
            ownsData = true;
        }
        else
        {
            data = nullptr;
        }
    }

    // 赋值操作符
    BluetoothFrame &operator=(const BluetoothFrame &other)
    {
        if (this != &other)
        {
            // 清理现有资源
            cleanup();

            // 复制基本数据
            command = other.command;
            dataLength = other.dataLength;
            isValid = other.isValid;
            timestamp = other.timestamp;

            // 深拷贝数据
            if (other.data && other.dataLength > 0)
            {
                data = new uint8_t[other.dataLength];
                memcpy(data, other.data, other.dataLength);
                ownsData = true;
            }
            else
            {
                data = nullptr;
                ownsData = false;
            }

            // 重置转换状态
            convertedData = nullptr;
            isConverted = false;
        }
        return *this;
    }

    ~BluetoothFrame()
    {
        cleanup();
    }

private:
    void cleanup()
    {
        if (convertedData)
        {
            delete[] convertedData;
            convertedData = nullptr;
        }
        if (ownsData && data)
        {
            delete[] data;
        }
        data = nullptr;
        ownsData = false;
        isConverted = false;
    }

public:
    // 数据解析辅助方法
    String getTextData() const;
    void getColorData(uint8_t &screenArea, uint8_t &target, uint8_t &mode, uint8_t &r, uint8_t &g, uint8_t &b, uint8_t &gradientMode) const;
    uint8_t getBrightnessData() const;
    void getEffectData(uint8_t &screenArea, uint8_t &type, uint8_t &speed) const;
    void getBorderData(uint8_t &style, uint8_t &colorIndex, uint8_t &effect, uint8_t &speed) const;
    bool isValidCommand() const;
    bool hasValidData() const { return isValid && data != nullptr && dataLength > 0; }
    // 字体数据转换方法
    const uint16_t *getFontData16x16(uint8_t &screenArea, int &charCount) const;
    const uint16_t *getFontData32x32(uint8_t &screenArea, int &charCount) const;
    // 文件传输相关方法
    void getFileStartData(String &filename, uint32_t &fileSize) const;
    String getFileDeleteData() const;
    String getPlayGifData() const;
    // 分区模式数据解析
    uint8_t getSplitModeData() const;
            // 时钟模式和计分模式数据解析方法
    void getClockModeData(uint8_t &screenMode, uint8_t &gifSelect,
                         uint8_t &weekdayR, uint8_t &weekdayG, uint8_t &weekdayB,
                         uint8_t &timeR, uint8_t &timeG, uint8_t &timeB,
                         uint8_t &hour, uint8_t &minute, uint8_t &weekday, uint8_t &language) const;
    void getScoreModeTextDataWithColor(uint8_t &region, uint8_t &colorR, uint8_t &colorG, uint8_t &colorB,
                                      uint8_t* textData, uint16_t &textDataLength) const; // 修正版本，包含RGB颜色解析
    void getScoreModeNumData(uint8_t &region, uint8_t &colorR, uint8_t &colorG, uint8_t &colorB,
                            uint16_t &initValue, uint8_t &operation, uint16_t &operValue) const;
    
};

class BluetoothProtocolParser
{
private:
    ParseState currentState;
    uint8_t command;
    uint16_t dataLength;
    uint16_t dataReceived;
    uint8_t *dataBuffer;
    uint32_t frameStartTime;      // 帧开始时间
    uint32_t errorCount;          // 错误计数
    uint16_t timeoutCheckCounter; // 超时检查计数器
    static const uint16_t MAX_DATA_LENGTH = 8192;
    static const uint32_t FRAME_TIMEOUT_MS = 5000; // 帧超时时间

public:
    BluetoothProtocolParser();
    ~BluetoothProtocolParser();

    ParseResult parseByte(uint8_t byte, BluetoothFrame &frame);
    ParseResult parseBuffer(uint8_t *buffer, size_t length, BluetoothFrame frames[], size_t maxFrames, size_t &frameCount);
    void reset();
    void Error_reset();
    bool isFrameComplete() const;
    bool isFrameTimeout() const;
    uint32_t getErrorCount() const { return errorCount; }
    ParseState getCurrentState() const { return currentState; }

    // 调试功能
    String getStateString() const;
    void printDebugInfo() const;
};

#endif // BLUETOOTH_PROTOCOL_H
