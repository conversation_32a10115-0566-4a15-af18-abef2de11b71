#include "app_list.h"

 int list_num=0;
 size_t namelen_list=0;
 const char * filename = nullptr; // 当前文件名
 String fullPath = "";
extern bool isnext_list_name; // 是否切换下一个文件名
extern bool is_gif_exist_listname; // 是否存在文件名

// 存储文件名的动态数组
char **file_list = (char **)malloc(MAX_FILES * sizeof(char *));
int file_count = 0;

void initFileList() {
    // 初始化文件列表
    for (int i = 0; i < MAX_FILES; i++) {
        file_list[i] = nullptr;
    }
    file_count = 0;
}

void freeFileList() {
    // 释放文件列表内存
    for (int i = 0; i < file_count; i++) {
        if (file_list[i]) {
            free(file_list[i]);
            file_list[i] = nullptr; // 避免悬空指针
        }
    }
    file_count = 0;
}

void addFileToList(const char* filename) {
    if (file_count < MAX_FILES) {
        file_list[file_count] = (char *)malloc((strlen(filename) + 1) * sizeof(char));
        strcpy(file_list[file_count], filename);
        file_count++;
    } else {
        // 超出最大文件数量，处理错误
        DEBUG_ERROR("File list is full, cannot add more files");
    }
}

void printFileList() {
    // 打印文件列表
    for (int i = 0; i < file_count; i++) {
        if (file_list[i]) {
            Serial.println(file_list[i]);
        }
    }
}

int processFileList() {

        if(file_count>MAX_FILES)
        {
            DEBUG_WARN("too much file_count");
            return LIST_NO_MODE;
        }
        

        filename = file_list[list_num%file_count];
        while (filename == nullptr || strlen(filename) == 0) {
            DEBUG_WARN("Empty filename at index %d", list_num);
            list_num++;
            char *filename = file_list[list_num%file_count];
        }
        
        // 获取文件名长度
        namelen_list = strlen(filename);

        list_num++;
        
      
        // 检查是否为 .gif 文件（至少5个字符：x.gif）
        if (namelen_list >= 5 && strcmp(filename + namelen_list - 4, ".gif") == 0) {
        
            if(bool a =play_list_GIF())
            {
                DEBUG_INFO("GIF file detected: %s", filename);
                isnext_list_name = false; // 设置为不切换下一个文件
            }
            else
            {
                isnext_list_name = true; // 设置为切换下一个文件
            }
            return LIST_GIF_MODE;
        }
        // 检查是否为 .text 文件（至少6个字符：x.text）
        else if (namelen_list >= 6 && strcmp(filename + namelen_list - 5, ".text") == 0) {
            return LIST_TEXT_MODE;
        }
        else {
            DEBUG_WARN("未知文件类型: %s\n", filename);
            return LIST_UNKNOW;
        }
}

bool play_list_GIF()
{
    fullPath = GIF_STORAGE_PATH + String(filename);

    
    if (!LittleFS.exists(fullPath)) {
        Serial.printf("GIF file not found: %s\n", filename);
        return false;
    }

    File file = LittleFS.open(fullPath, "r");
    if (!file) {
        Serial.printf("Cannot open GIF file: %s\n", filename);
        return false;
    }

    size_t fileSize = file.size();
    file.close();

    Serial.printf("Auto-selecting playback mode for %s (size: %d bytes)\n", filename, fileSize);
    return playGIFStream(fullPath.c_str());
}

void updateGIF_FOR_list()
{
    if (!gifPlaying) {
        return;
    }

    unsigned long currentTime = millis();
    if (currentTime - lastGifFrame >= gifFrameDelay) {
        int result = gif.playFrame(true, NULL);
        if (result == 0) {
            gif.close();
            isnext_list_name = true; // 设置为切换下一个文件
            
        } else if (result < 0) {
            // 播放出错，停止播放 - 只在错误级别输出，避免频繁打印
            GIF_ERROR("GIF playback error, stopping");
            stopGIF();
            return;
        }
        lastGifFrame = currentTime;
    }
}

// 处理列表开始 (0x30)
void handleAppListStartCommand(const BluetoothFrame &frame)
{
    freeFileList(); // 清除之前的文件列表
    DEBUG_INFO("📂 start to receive list");
}

// 处理列表数据 (0x31)
void handleAppListDataCommand(const BluetoothFrame &frame)
{
    if (!frame.hasValidData() || frame.dataLength < 1)
    {
        DEBUG_ERROR("❌ invalid list data");
        return;
    }

    String filename_forlist;
    filename_forlist=String((char *)frame.data,frame.dataLength);
    addFileToList(filename_forlist.c_str());
}

void handleAppListEndCommand(const BluetoothFrame &frame)
{
    DEBUG_INFO("✅ file list received");
    printFileList();
}