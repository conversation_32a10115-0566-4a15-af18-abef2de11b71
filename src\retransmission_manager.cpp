#include "retransmission_manager.h"
#include "BluetoothSerial.h"
#include "debug_utils.h"

extern BluetoothSerial SerialBT;

RetransmissionManager::RetransmissionManager() {
    activeEntryIndex = -1;
    reset();
}

void RetransmissionManager::sendResponseCode(uint8_t code) {
    SerialBT.write(code);
    FILE_DEBUG("Sent response code: 0x%02X", code);
}

int RetransmissionManager::findFreeEntry() {
    for (int i = 0; i < RETRANS_MAX_ACTIVE_ENTRIES; i++) {
        if (!entries[i].active) {
            return i;
        }
    }
    return -1; // 没有可用条目
}

void RetransmissionManager::clearEntry(int index) {
    if (index >= 0 && index < RETRANS_MAX_ACTIVE_ENTRIES) {
        entries[index].active = false;
        entries[index].attemptCount = 0;
        entries[index].lastSendTime = 0;
        entries[index].responseCode = 0;
        
        if (activeEntryIndex == index) {
            activeEntryIndex = -1;
        }
    }
}

void RetransmissionManager::sendWithRetransmission(uint8_t responseCode) {
    // 先清除之前的活跃条目(文件传输通常是串行的)
    if (activeEntryIndex >= 0) {
        clearEntry(activeEntryIndex);
    }
    
    // 查找可用条目
    int entryIndex = findFreeEntry();
    if (entryIndex == -1) {
        FILE_WARN("No free retransmission entry, sending directly");
        sendResponseCode(responseCode);
        return;
    }
    
    // 设置重传条目
    RetransmissionEntry& entry = entries[entryIndex];
    entry.responseCode = responseCode;
    entry.lastSendTime = millis();
    entry.attemptCount = 1;  // 第一次发送
    entry.active = true;
    
    activeEntryIndex = entryIndex;
    
    // 立即发送响应
    sendResponseCode(responseCode);
    
    FILE_DEBUG("Started retransmission for code 0x%02X, entry %d", responseCode, entryIndex);
}

void RetransmissionManager::onDataReceived() {
    // 接收到新数据，取消所有等待中的重传
    if (activeEntryIndex >= 0) {
        FILE_DEBUG("Data received, canceling retransmission for entry %d", activeEntryIndex);
        clearEntry(activeEntryIndex);
    }
}

void RetransmissionManager::checkTimeouts() {
    if (activeEntryIndex < 0) {
        return; // 没有活跃条目
    }
    
    RetransmissionEntry& entry = entries[activeEntryIndex];
    if (!entry.active) {
        activeEntryIndex = -1;
        return;
    }
    
    uint32_t currentTime = millis();
    uint32_t timeSinceLastSend = currentTime - entry.lastSendTime;
    
    // 检查是否超时
    if (timeSinceLastSend >= RETRANS_TIMEOUT_MS) {
        if (entry.attemptCount < RETRANS_MAX_ATTEMPTS) {
            // 执行重传
            entry.attemptCount++;
            entry.lastSendTime = currentTime;
            
            FILE_WARN("Retransmission timeout, sending BT_ERROR_FRAME_FORMAT (attempt %d/%d)", 
                      entry.attemptCount, RETRANS_MAX_ATTEMPTS);
            
            sendResponseCode(BT_NEXT_FRAME);
        } else {
            // 达到最大重传次数，中断传输
            FILE_ERROR("Max retransmission attempts reached, aborting transmission");
            
            // 发送最后一次错误响应
            sendResponseCode(BT_NEXT_FRAME);
            
            // 清除条目并中断传输
            clearEntry(activeEntryIndex);
            
            // 这里可以调用文件传输中断函数
            // 由于需要避免循环依赖，这部分逻辑可能需要在调用方处理
        }
    }
}

void RetransmissionManager::abortTransmission() {
    if (activeEntryIndex >= 0) {
        FILE_INFO("Manually aborting retransmission for entry %d", activeEntryIndex);
        clearEntry(activeEntryIndex);
    }
}

int RetransmissionManager::getActiveEntryCount() const {
    int count = 0;
    for (int i = 0; i < RETRANS_MAX_ACTIVE_ENTRIES; i++) {
        if (entries[i].active) {
            count++;
        }
    }
    return count;
}

void RetransmissionManager::reset() {
    for (int i = 0; i < RETRANS_MAX_ACTIVE_ENTRIES; i++) {
        clearEntry(i);
    }
    activeEntryIndex = -1;
    FILE_DEBUG("Retransmission manager reset");
}