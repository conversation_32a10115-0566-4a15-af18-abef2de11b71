# 分区显示蓝牙帧格式说明

## 分区显示设置命令 (0x0C)

### 命令格式

```
AA 55 0C 00 01 [分区模式] 0D 0A
```

### 参数说明

- 命令码：0x0C
- 数据长度：1 字节
- 分区模式：选择分区显示状态

## 参数详细说明

### 分区模式 (第 1 字节)

| 值   | 模式          | GIF 位置       | 文字位置        | 字体  | 说明                       |
| ---- | ------------- | -------------- | --------------- | ----- | -------------------------- |
| 0x00 | 退出分区模式  | 无             | 全屏(0,0,64,32) | 16x16 | 恢复全屏文本显示           |
| 0x01 | GIF 左+文字右 | 左(0,0,32,32)  | 右(32,0,32,32)  | 32x32 | GIF 在左侧，单行文字在右侧 |
| 0x02 | GIF 左+文字右 | 左(0,0,32,32)  | 右(32,0,32,32)  | 16x16 | GIF 在左侧，双行文字在右侧 |
| 0x03 | GIF 右+文字左 | 右(32,0,32,32) | 左(0,0,32,32)   | 32x32 | GIF 在右侧，单行文字在左侧 |
| 0x04 | GIF 右+文字左 | 右(32,0,32,32) | 左(0,0,32,32)   | 16x16 | GIF 在右侧，双行文字在左侧 |

## 使用示例

### 退出分区模式

```
AA 55 0C 00 01 00 0D 0A
```

- 恢复全屏 64x32 显示
- 字体自动重置为 16x16 双行模式
- 文字重新布局为上下半屏显示

### GIF 左侧 + 32x32 文字右侧

```
AA 55 0C 00 01 01 0D 0A
```

- GIF 显示区域：左侧 32x32 像素(0,0,32,32)
- 文字显示区域：右侧 32x32 像素(32,0,32,32)
- 字体自动切换为 32x32 单行模式

### GIF 左侧 + 16x16 双行文字右侧

```
AA 55 0C 00 01 02 0D 0A
```

- GIF 显示区域：左侧 32x32 像素(0,0,32,32)
- 文字显示区域：右侧 32x32 像素(32,0,32,32)
- 字体自动切换为 16x16 双行模式
- 上半行：右侧上半部分(32,0,32,16)
- 下半行：右侧下半部分(32,16,32,16)

### GIF 右侧 + 32x32 文字左侧

```
AA 55 0C 00 01 03 0D 0A
```

- GIF 显示区域：右侧 32x32 像素(32,0,32,32)
- 文字显示区域：左侧 32x32 像素(0,0,32,32)
- 字体自动切换为 32x32 单行模式

### GIF 右侧 + 16x16 双行文字左侧

```
AA 55 0C 00 01 04 0D 0A
```

- GIF 显示区域：右侧 32x32 像素(32,0,32,32)
- 文字显示区域：左侧 32x32 像素(0,0,32,32)
- 字体自动切换为 16x16 双行模式
- 上半行：左侧上半部分(0,0,32,16)
- 下半行：左侧下半部分(0,16,32,16)

## 完整使用流程

### 1. 设置分区模式

```
AA 55 0C 00 01 01 0D 0A  // 设置为GIF左+32x32文字右
```

### 2. 播放 GIF 文件

```
AA 55 15 00 08 test.gif 0D 0A  // 播放test.gif，自动显示在左侧
```

### 3. 发送文字内容

```
AA 55 04 00 81 03 [128字节字体数据] 0D 0A  // 发送32x32文字，自动显示在右侧
```

### 4. 动态切换布局

```
AA 55 0C 00 01 03 0D 0A  // 切换为GIF右+文字左模式
```

### 5. 退出分区模式

```
AA 55 0C 00 01 00 0D 0A  // 退出分区，恢复全屏显示
```

## 注意事项

1. **GIF 尺寸要求**：推荐使用 32x32 尺寸的 GIF 文件以获得最佳显示效果
2. **自动字体切换**：设置分区模式时系统会自动切换到对应的字体大小
3. **文字区域限制**：分区模式下文字显示区域为 32x32 像素
4. **坐标自动计算**：GIF 和文字的显示位置由系统自动计算，无需手动指定
5. **状态保持**：切换分区模式时会保持当前的文字内容
6. **兼容性**：所有现有的文字、颜色、特效、边框命令在分区模式下正常工作
7. **边框范围**：边框效果仅在文字区域内显示，不会影响 GIF 区域
8. **退出重置**：退出分区模式时字体会自动重置为默认的 16x16 双行模式

## 分区模式与其他命令的配合

### 文字命令 (0x04)

- 在分区模式下，文字会自动显示在指定的文字区域内
- 字体大小由分区模式自动决定，无需额外设置

### 颜色命令 (0x06)

- 颜色设置仅影响文字区域，不会影响 GIF 显示
- 支持文字颜色、背景颜色、渐变色等所有颜色功能

### 特效命令 (0x08)

- 滚动、闪烁、呼吸等特效仅在文字区域内生效
- 特效不会影响 GIF 的播放和显示

### 边框命令 (0x09)

- 边框仅在文字区域内绘制
- 支持实线、点线、角落、彩虹等所有边框样式

### GIF 播放命令 (0x15)

- 在分区模式下播放 GIF 时，GIF 会自动显示在指定位置
- 非分区模式下，GIF 会居中全屏显示
