# APP 端列表功能数据发送规范

## 概述

本文档定义了 APP 端向 ESP32 设备发送播放列表数据的具体格式和示例，包含三种列表项类型：纯 GIF、纯文本、GIF+文本组合。APP 端负责管理播放顺序，按序发送数据到单片机进行循环播放。

## 数据结构定义

### 通用列表项头部结构 (8 字节) - 简化版

````cpp
struct ListItemHeader {
    uint8_t type;           // 项目类型：0x01=GIF, 0x02=文本, 0x03=组合
    uint8_t version;        // 版本号：0x01
    uint16_t dataLength;    // 配置数据长度
    uint8_t reserved[4];    // 预留字段（填充0x00）
};


### GIF 配置结构 (48 字节)

```cpp
struct GifConfig {
    char gifFilename[32];   // GIF文件名（包含.gif扩展名）
    uint8_t reserved[16];   // 预留字段，用于未来扩展
};
````

### 文本配置结构 (3217 字节)

```cpp
struct TextConfig {
    uint8_t fontType;       // 字体类型：0x00=16x16, 0x01=32x32
    uint8_t screenArea;     // 显示区域：0x01=上半屏, 0x02=下半屏, 0x03=全屏
    uint8_t textColor[3];   // RGB文本颜色
    uint8_t bgColor[3];     // RGB背景颜色（叠层模式下无效）
    uint8_t effect;         // 特效类型：0x00=静态, 0x01=左滚, 0x02=右滚等
    uint8_t effectSpeed;    // 特效速度：1-10
    uint8_t borderStyle;     // 边框样式：0x00=无边框，0x01=实线边框，0x02=点线边框，0x03=角落边框，0x04=彩虹边框
    uint8_t borderColorIndex; // 边框颜色索引：0x00=红色，0x01=绿色，0x02=蓝色，0x03=黄色，0x04=紫色，0x05=青色，0x06=白色
    uint8_t borderEffect;    // 边框效果：0x00=静止显示，0x01=顺时针流动，0x02=逆时针流动，0x03=闪烁效果
    uint8_t borderSpeed;     // 边框速度：1-10（1=最慢，10=最快）
    uint8_t gradientMode;   // 渐变模式：0x00=无, 0x01=垂直, 0x02=水平
    uint8_t charCount;      // 字符数量：1-100
    uint8_t fontData[3200]; // 点阵数据（最大支持100个32x32字符：100×32=3200字节）
};
```

### 组合配置结构 (3249 字节)

```cpp
struct ComboConfig {
    char gifFilename[32];   // GIF文件名
    TextConfig textConfig; // 文本配置（复用TextConfig结构）
};
```

## 蓝牙协议格式

### 基本命令结构

```
AA 55 [命令] [长度高] [长度低] [数据] 0D 0A
```

### 列表相关命令定义

- `0x30` - 列表开始命令
- `0x33` - 添加 GIF 项命令
- `0x34` - 添加文本项命令
- `0x35` - 添加组合项命令
- `0x32` - 列表结束命令

### 数据包长度计算

- **GIF 项目**：8(Header) + 48(GifConfig) = 56 字节
- **文本项目**：8(Header) + 3217(TextConfig) = 3225 字节
- **组合项目**：8(Header) + 3249(ComboConfig) = 3257 字节

## 数据填充说明

### 什么是填充？

"填充"是指用`0x00`填充数据结构中未使用的空间，确保数据包的完整性和一致性。

### 填充的必要性

1. **固定结构大小**：确保发送和接收端的数据结构大小完全一致
2. **避免垃圾数据**：防止未初始化的内存包含随机值
3. **协议稳定性**：保证数据传输的可靠性

### 填充示例

#### 字符串填充

```cpp
char gifFilename[32];  // 定义32字节数组

// 文件名："demo.gif" (8个字符 + 1个结束符 = 9字节)
// 实际存储：
// 位置 0-7:   'd' 'e' 'm' 'o' '.' 'g' 'i' 'f'
// 位置 8:     '\0' (字符串结束符)
// 位置 9-31:  0x00 0x00 0x00 ... (23个0填充)
```

#### 点阵数据填充

```cpp
uint8_t fontData[3200];  // 定义3200字节数组（支持100个字符）

// 实际只有5个16x16字符 (5×32=160字节有效数据)
// 存储布局：
// 位置 0-159:   [字符1点阵32字节] + [字符2点阵32字节] + ... + [字符5点阵32字节]
// 位置 160-3199: 0x00 0x00 0x00 ... (3040个0填充)
```

#### 预留字段填充

```cpp
uint8_t reserved[4];  // 预留字段

// 全部填充为0：
// reserved[0] = 0x00
// reserved[1] = 0x00
// reserved[2] = 0x00
// reserved[3] = 0x00
```

## APP 端发送示例

### 示例 1：完整播放列表发送流程

假设要发送包含 3 个项目的播放列表：开场 GIF → 欢迎文字 → 背景+问候语

#### 步骤 1：发送列表开始命令

```
十六进制数据包：
AA 55 30 00 00 0D 0A

解析：
- 帧头：AA 55
- 命令：30 (列表开始)
- 长度：00 00 (0字节数据)
- 数据：无
- 帧尾：0D 0A
```

#### 步骤 2：发送 GIF 项目

```
十六进制数据包：
AA 55 33 00 38
[8字节ListItemHeader]
01                                    // type: GIF项目
01                                    // version: 版本1
00 30                                 // dataLength: 48字节
00 00 00 00                          // reserved: 4字节预留

[48字节GifConfig]
6F 70 65 6E 69 6E 67 2E 67 69 66 00  // gifFilename: "opening.gif"
00 00 00 00 00 00 00 00 00 00 00 00   // gifFilename填充
00 00 00 00 00 00 00 00              // gifFilename填充
00 00 00 00 00 00 00 00 00 00 00 00   // reserved: 16字节预留
00 00 00 00

0D 0A                                // 帧尾

总长度：7 + 56 = 63字节
```

#### 步骤 3：发送文本项目

```
十六进制数据包：
AA 55 34 0C 98
[8字节ListItemHeader]
02                                    // type: 文本项目
01                                    // version: 版本1
0C 91                                 // dataLength: 3217字节
00 00 00 00                          // reserved: 4字节预留

[3217字节TextConfig]
00                                    // fontType: 16x16字体
03                                    // screenArea: 全屏显示
FF FF FF                             // textColor: 白色文字
00 00 00                             // bgColor: 黑色背景
01                                    // effect: 左滚特效
05                                    // effectSpeed: 速度5
00                                    // borderStyle: 无边框
00                                    // borderColorIndex: 红色（无边框时忽略）
00                                    // borderEffect: 静止显示（无边框时忽略）
01                                    // borderSpeed: 速度1（无边框时忽略）
00                                    // gradientMode: 无渐变
04                                    // charCount: 4个字符
[128字节点阵数据]                      // "欢迎光临"的16x16点阵数据
[3089字节填充]                        // 剩余空间填充0

0D 0A                                // 帧尾

总长度：7 + 3225 = 3232字节
```

#### 步骤 4：发送组合项目

```
十六进制数据包：
AA 55 35 0C B8
[8字节ListItemHeader]
03                                    // type: 组合项目
01                                    // version: 版本1
0C B1                                 // dataLength: 3249字节
00 00 00 00                          // reserved: 4字节预留

[3249字节ComboConfig]
62 61 63 6B 67 72 6F 75 6E 64 2E 67  // gifFilename: "background.gif"
69 66 00 00 00 00 00 00 00 00 00 00   // gifFilename填充
00 00 00 00 00 00 00 00 00 00 00 00   // gifFilename填充

[3217字节TextConfig - 与步骤3相同的文本配置]

0D 0A                                // 帧尾

总长度：7 + 3257 = 3264字节
```

#### 步骤 5：发送列表结束命令

```
十六进制数据包：
AA 55 32 00 00 0D 0A

解析：
- 帧头：AA 55
- 命令：32 (列表结束)
- 长度：00 00 (0字节数据)
- 数据：无
- 帧尾：0D 0A
```

### 示例 2：纯文本项目详细配置

#### 英文文本："HELLO" (5 个 ASCII 字符)

```
配置参数：
- 项目名称："英文问候"
- 字体类型：16x16
- 显示区域：上半屏
- 文本颜色：绿色 (0x00FF00)
- 背景颜色：黑色 (0x000000)
- 特效类型：闪烁
- 特效速度：3
- 边框样式：实线
- 边框颜色：红色 (0xFF0000)
- 渐变模式：垂直渐变

数据包：
AA 55 34 0C 98
[ListItemHeader]
02 01 0C 91 00 00 00 00  // type, version, dataLength(3217), reserved

[TextConfig]
00          // fontType: 16x16
01          // screenArea: 上半屏
00 FF 00    // textColor: 绿色
00 00 00    // bgColor: 黑色
03          // effect: 闪烁
03          // effectSpeed: 速度3
01          // borderStyle: 实线边框
00          // borderColorIndex: 红色
00          // borderEffect: 静止显示
05          // borderSpeed: 速度5
01          // gradientMode: 垂直渐变
05          // charCount: 5字符
[160字节]   // "HELLO"点阵数据(5×32字节)
[3057字节] // 剩余空间填充0

0D 0A
```

### 示例 3：中文文本项目

#### 中文文本："你好世界" (4 个汉字)

```
配置参数：
- 项目名称："中文显示"
- 字体类型：32x32
- 显示区域：全屏
- 文本颜色：蓝色 (0x0000FF)
- 特效类型：右滚
- 特效速度：7

数据包：
AA 55 34 0C 98
[ListItemHeader]
02 01 0C 91 00 00 00 00  // type, version, dataLength(3217), reserved

[TextConfig]
01          // fontType: 32x32
03          // screenArea: 全屏
00 00 FF    // textColor: 蓝色
00 00 00    // bgColor: 黑色
02          // effect: 右滚
07          // effectSpeed: 速度7
00          // borderStyle: 无边框
00          // borderColorIndex: 红色（无边框时忽略）
00          // borderEffect: 静止显示（无边框时忽略）
01          // borderSpeed: 速度1（无边框时忽略）
00          // gradientMode: 无渐变
04          // charCount: 4字符
[256字节]   // "你好世界"点阵数据(4×64字节)
[2961字节] // 剩余空间填充0

0D 0A
```

## APP 端实现要点

### 1. 发送流程控制

```
播放列表发送流程：
1. 用户点击"发送到设备"
2. 发送列表开始命令 (0x30)
3. 按用户设定的播放顺序逐个发送列表项
4. 发送列表结束命令 (0x32)
5. 等待设备确认响应
```

### 2. 数据验证检查

- **文件名长度**：不超过 31 个字符（预留 1 个结束符）
- **字符数量限制**：
  - 16x16 字体：最多 100 个字符（100×32=3200 字节）
  - 32x32 字体：最多 50 个字符（50×64=3200 字节）
- **颜色值范围**：RGB 各分量 0-255
- **特效参数范围**：速度 1-10，类型 0-8
- **边框参数验证**：
  - 边框样式：0-4（超出范围拒绝执行）
  - 边框颜色索引：0-6（超出范围拒绝执行）
  - 边框效果：0-3（超出范围拒绝执行）
  - 边框速度：1-10（超出范围拒绝执行）

### 成功响应

```
命令成功：返回 0xC0 (BT_NEXT_FRAME)
列表接收完成：返回 "OK:List received"
```

### 错误响应

```
数据格式错误：返回 "ERROR:Invalid format"
内存不足：返回 "ERROR:Memory full"
文件不存在：返回 "ERROR:File not found"
```

## 调试和测试

### 数据包验证工具

建议 APP 端提供数据包预览功能，显示：

- 十六进制数据内容
- 数据包大小统计
- 配置参数解析结果
- 点阵数据可视化预览

### 测试用例

1. **单项目测试**：分别测试三种类型的单个项目
2. **混合列表测试**：包含所有类型的组合列表
3. **边界条件测试**：最大字符数、最长文件名等
4. **异常情况测试**：传输中断、设备断开等
