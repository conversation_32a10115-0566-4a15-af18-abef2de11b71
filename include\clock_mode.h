#ifndef CLOCK_MODE_H
#define CLOCK_MODE_H

#include <Arduino.h>
#include <ESP32-HUB75-MatrixPanel-I2S-DMA.h>




// 区域定义结构体
typedef struct {
    // 区域边界坐标
    uint8_t x1, y1;  // 左上角坐标
    uint8_t x2, y2;  // 右下角坐标

    // 显示内容
    uint8_t* bitmap_data;     // 点阵数据指针
    uint16_t data_width;      // 数据宽度
    uint16_t data_height;     // 数据高度

    // 显示位置
    int8_t cursor_x;          // 在区域内的光标X位置
    int8_t cursor_y;          // 在区域内的光标Y位置

    // 颜色设置
    uint16_t text_color;      // 文本颜色 (RGB565)
    uint16_t bg_color;        // 背景颜色 (RGB565)

    // 显示属性
    bool auto_clear;          // 是否自动清除背景
    bool visible;             // 是否可见
    uint8_t update_flag;      // 更新标志位
} DisplayRegion;

// 时间信息结构体
typedef struct {
    uint8_t hour;
    uint8_t minute;
    uint8_t weekday;  // 0-6 (周日到周六)
    uint16_t time_color;
    uint16_t weekday_color;
} TimeDisplayInfo;

// 语言枚举定义（必须在函数声明之前定义）
typedef enum {
    LANG_CHINESE = 0,
    LANG_ENGLISH = 1,
    LANG_COUNT
} WeekdayLanguage;

// 显示模式枚举
typedef enum {
    DISPLAY_MODE_SMALL = 0,  // 小屏模式（原有三分区）
    DISPLAY_MODE_BIG = 1     // 大屏模式（右侧大区域）
} DisplayMode;

// 大屏显示状态枚举
typedef enum {
    BIG_SCREEN_TIME = 0,     // 显示时间
    BIG_SCREEN_WEEKDAY = 1   // 显示星期
} BigScreenState;

// ==================== 从原始gif_player.h移动的时钟相关函数声明 ====================

// 显示区域相关函数声明
void initDisplayRegions();
void setupTimeRegions();
void updateTimeDisplay();
void updateAllRegions();

// 区域操作函数
void initDisplayRegion(DisplayRegion* region, uint8_t x1, uint8_t y1,
                      uint8_t x2, uint8_t y2, uint16_t text_color, uint16_t bg_color);
void clearRegion(DisplayRegion* region);
void updateRegion(DisplayRegion* region);
bool isPointInRegion(DisplayRegion* region, uint8_t x, uint8_t y);
void forceClearRegion(int16_t x1, int16_t y1, int16_t w, int16_t h);
// 绘制函数
void drawBitmapToRegion(DisplayRegion* region, const uint8_t* bitmap,
                       uint8_t width, uint8_t height, int8_t offset_x, int8_t offset_y);
void drawCharToRegion(DisplayRegion* region, uint8_t char_index, int8_t x, int8_t y);
void drawWeekdayCharToRegion(DisplayRegion* region, uint8_t char_index, int8_t x, int8_t y);
void drawEnglishWeekdayCharToRegion(DisplayRegion* region, uint8_t char_index, int8_t x, int8_t y);
void drawTimeToRegion(DisplayRegion* region, uint8_t hour, uint8_t minute);
void drawWeekdayToRegion(DisplayRegion* region, uint8_t weekday);
void drawWeekdayToRegionWithLang(DisplayRegion* region, uint8_t weekday, WeekdayLanguage language);

// 时间管理函数
void initTimeSystem();
void updateTimeLogic();
void setTime(uint8_t hour, uint8_t minute);
void setTimeAndWeekday(uint8_t hour, uint8_t minute, uint8_t weekday);
void setTimeAndWeekdayWithLang(uint8_t hour, uint8_t minute, uint8_t weekday, WeekdayLanguage language);
void setTimeColor(uint16_t color);
void setWeekdayColor(uint16_t color);
void setWeekday(uint8_t weekday);
void setWeekdayWithLanguage(uint8_t weekday, WeekdayLanguage language);
const char* getWeekdayName(uint8_t weekday);
const char* getWeekdayNameWithLang(uint8_t weekday, WeekdayLanguage language);

// 全局语言管理函数
void setSystemDisplayLanguage(WeekdayLanguage language);
WeekdayLanguage getSystemDisplayLanguage();
void updateWeekdayWithCurrentLanguage(uint8_t weekday);

// ==================== 时钟模式状态管理函数 ====================
void enterClockMode();    // 进入时钟模式，自动退出计分模式
void exitClockMode();     // 退出时钟模式
bool isClockModeActive(); // 检查时钟模式状态

// 大屏模式核心函数
void setDisplayMode(DisplayMode mode);
void updateBigScreenDisplay();
void updateRegionVisibility();
void forceClearRegion(DisplayRegion* region);

// 大屏绘制函数
void drawBigScreenTime(bool force_redraw);
void drawBigScreenWeekday(bool force_redraw);
void drawBig32x8TimeChar(DisplayRegion* region, uint8_t char_index, int x, int y);
void drawBig32x16ChineseChar(DisplayRegion* region, uint8_t char_index, int x, int y);
void drawBig32x16EnglishChars(DisplayRegion* region, uint8_t weekday, int x, int y);

// 大屏配置函数
void setBigScreenSwitchInterval(unsigned long interval_ms);
void setBigScreenLanguage(WeekdayLanguage language);
void setBigScreenTimeAndWeekday(uint8_t hour, uint8_t minute, uint8_t weekday, WeekdayLanguage language);

// 大屏颜色控制函数
void setBigScreenTimeColor(uint16_t color);
void setBigScreenWeekdayColor(uint16_t color);
void setBigScreenBackgroundColor(uint16_t color);
void setBigScreenColorTheme(uint16_t time_color, uint16_t weekday_color, uint16_t bg_color);

// 测试函数（参数化版本）
void testBigScreenMode(uint8_t hour, uint8_t minute, uint8_t weekday, WeekdayLanguage language);

// 工具函数
uint8_t getCharIndex(char c);

// ==================== 蓝牙控制接口 ====================
void handleBluetoothClockMode(uint8_t screenMode, uint8_t gifSelect,
                             uint8_t weekdayR, uint8_t weekdayG, uint8_t weekdayB,
                             uint8_t timeR, uint8_t timeG, uint8_t timeB,
                             uint8_t hour, uint8_t minute, uint8_t weekday, uint8_t language);

// 字体尺寸
#define FONT_WIDTH     8
#define FONT_HEIGHT    16
#define WEEKDAY_FONT_WIDTH  16
#define WEEKDAY_FONT_HEIGHT 16
// 区域ID定义
#define REGION_GIF         0  // 左侧 16x32 - GIF区域（不变）
#define REGION_TIME        1  // 右上 48x16 - 时间区域（小屏模式）
#define REGION_WEEKDAY     2  // 右下 48x16 - 星期区域（小屏模式）
#define REGION_BIG_SCREEN  3  // 右侧 48x32 - 大屏区域（大屏模式）
#define MAX_REGIONS        4  // 更新最大区域数
// 默认时间设置
#define DEFAULT_HOUR   13
#define DEFAULT_MINUTE 12

extern MatrixPanel_I2S_DMA *dma_display;
extern WeekdayLanguage current_display_language;
// 大屏模式全局状态管理
extern DisplayMode current_display_mode;
extern BigScreenState big_screen_state;
extern unsigned long big_screen_last_switch;
extern unsigned long big_screen_switch_interval;
extern WeekdayLanguage big_screen_language;
extern uint16_t big_screen_time_color;
extern uint16_t big_screen_weekday_color;
extern uint16_t big_screen_bg_color;

// 颜色定义
#define COLOR_WHITE    0xFFFF
#define COLOR_RED      0xF800
#define COLOR_GREEN    0x07E0
#define COLOR_BLUE     0x001F
#define COLOR_YELLOW   0xFFE0
#define COLOR_CYAN     0x07FF
#define COLOR_MAGENTA  0xF81F
#define COLOR_BLACK    0x0000
#endif // CLOCK_MODE_H
