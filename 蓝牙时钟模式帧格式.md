# 蓝牙时钟模式帧格式说明

## 📋 概述

本文档描述时钟模式的蓝牙帧格式规范。时钟模式用于设置LED显示屏的时钟显示功能，包括屏幕模式选择、GIF背景、星期和时间的颜色设置、时间数值以及语言选择等参数。

## 🔧 命令定义

- **命令代码**: `0x20` (`BT_CMD_CLOCK_MODE`)
- **数据长度**: `12字节` (`BT_CLOCK_DATA_LEN`)
- **功能**: 设置时钟模式的显示参数

## 📦 帧格式

> **⚠️ 重要说明**: 数据长度字段采用双字节格式（高字节+低字节），这与协议解析器的实现保持一致。对于小于 256 字节的数据，高字节为 0x00。

### 时钟模式帧结构 (0x20)

```
| 帧头1 | 帧头2 | 命令 | 数据长度高字节 | 数据长度低字节 | 数据区域 | 帧尾1 | 帧尾2 |
|-------|-------|------|--------------|--------------|---------| ------|-------|
| 0xAA  | 0x55  | 0x20 |     0x00     |     0x0C     | 12字节数据| 0x0D  | 0x0A  |
```

#### 时钟模式数据区域 (12 字节)

```
| 字节位置 | 字段名称 | 数据类型 | 取值范围 | 说明 |
|----------|----------|----------|----------|------|
| 0        | 屏幕模式 | uint8_t  | 0x00-0x01 | 大小屏模式选择 |
| 1        | GIF选择  | uint8_t  | 0x00-0x07 | 背景GIF文件选择 |
| 2        | 星期颜色R | uint8_t  | 0-255    | 星期字体红色分量 |
| 3        | 星期颜色G | uint8_t  | 0-255    | 星期字体绿色分量 |
| 4        | 星期颜色B | uint8_t  | 0-255    | 星期字体蓝色分量 |
| 5        | 时间颜色R | uint8_t  | 0-255    | 时间字体红色分量 |
| 6        | 时间颜色G | uint8_t  | 0-255    | 时间字体绿色分量 |
| 7        | 时间颜色B | uint8_t  | 0-255    | 时间字体蓝色分量 |
| 8        | 小时     | uint8_t  | 0x00-0x17 | 时钟数字(0-23) |
| 9        | 分钟     | uint8_t  | 0x00-0x3B | 分钟数字(0-59) |
| 10       | 星期     | uint8_t  | 0x00-0x06 | 星期选择 |
| 11       | 语言     | uint8_t  | 0x00-0x01 | 语言选择 |
```

## 📊 参数说明

### 屏幕模式 (第 0 字节)

| 数值 | 宏定义                  | 说明   |
| ---- | ----------------------- | ------ |
| 0x00 | `BT_CLOCK_SCREEN_SMALL` | 小屏模式 |
| 0x01 | `BT_CLOCK_SCREEN_BIG`   | 大屏模式 |

### GIF选择 (第 1 字节)

| 数值 | 对应文件 | 说明 |
| ---- | -------- | ---- |
| 0x00 | gif0     | GIF文件0 |
| 0x01 | gif1     | GIF文件1 |
| 0x02 | gif2     | GIF文件2 |
| 0x03 | gif3     | GIF文件3 |
| 0x04 | gif4     | GIF文件4 |
| 0x05 | gif5     | GIF文件5 |
| 0x06 | gif6     | GIF文件6 |
| 0x07 | gif7     | GIF文件7 |

### RGB 颜色分量 (第 2-7 字节)

- **取值范围**: 每个分量 0-255
- **格式**: RGB888 标准格式
- **星期颜色**: 第2-4字节，控制星期显示的文字颜色
- **时间颜色**: 第5-7字节，控制时间显示的文字颜色

### 时间数值 (第 8-9 字节)

- **小时**: 0x00-0x17 (十进制 0-23)
- **分钟**: 0x00-0x3B (十进制 0-59)
- **格式**: 24小时制

### 星期选择 (第 10 字节)

| 数值 | 星期 | 中文显示 | 英文显示 |
| ---- | ---- | -------- | -------- |
| 0x00 | 周日 | 星期日   | Sunday   |
| 0x01 | 周一 | 星期一   | Monday   |
| 0x02 | 周二 | 星期二   | Tuesday  |
| 0x03 | 周三 | 星期三   | Wednesday|
| 0x04 | 周四 | 星期四   | Thursday |
| 0x05 | 周五 | 星期五   | Friday   |
| 0x06 | 周六 | 星期六   | Saturday |

### 语言选择 (第 11 字节)

| 数值 | 宏定义                  | 说明   |
| ---- | ----------------------- | ------ |
| 0x00 | `BT_CLOCK_LANG_CHINESE` | 中文显示 |
| 0x01 | `BT_CLOCK_LANG_ENGLISH` | 英文显示 |

## 使用示例

### 基本时钟设置
```
// 大屏模式，GIF0背景，白色星期，红色时间，显示15:30周三，中文
AA 55 20 00 0C 01 00 FF FF FF FF 00 00 0F 1E 03 00 0D 0A
```

### 小屏彩色时钟
```
// 小屏模式，GIF3背景，蓝色星期，绿色时间，显示09:45周五，英文
AA 55 20 00 0C 00 03 00 00 FF 00 FF 00 09 2D 05 01 0D 0A
```

### 常用颜色RGB值
| 颜色 | R | G | B | 十六进制 |
|------|---|---|---|----------|
| 白色 | 255 | 255 | 255 | FF FF FF |
| 红色 | 255 | 0 | 0 | FF 00 00 |
| 绿色 | 0 | 255 | 0 | 00 FF 00 |
| 蓝色 | 0 | 0 | 255 | 00 00 FF |
| 黄色 | 255 | 255 | 0 | FF FF 00 |
| 紫色 | 255 | 0 | 255 | FF 00 FF |
| 青色 | 0 | 255 | 255 | 00 FF FF |
| 黑色 | 0 | 0 | 0 | 00 00 00 |

## 注意事项

1. 时钟模式数据长度固定为12字节
2. 时间采用24小时制格式
3. 星期编号从周日(0)开始到周六(6)
4. RGB颜色值范围为0-255
5. GIF文件编号对应系统中预设的背景动画
6. 语言设置影响星期的显示文字
7. 屏幕模式决定时钟显示的尺寸和布局
8. 设置立即生效，会触发时钟显示更新