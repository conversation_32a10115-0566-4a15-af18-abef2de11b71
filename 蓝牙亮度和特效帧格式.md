# 蓝牙帧格式说明文档

## 基本帧结构
```
[帧头1][帧头2][命令][数据长度][数据内容][帧尾1][帧尾2]
```

- 帧头：0xAA 0x55
- 帧尾：0x0D 0x0A
- 数据长度：2字节，表示数据内容的字节数

## 亮度设置命令 (0x07)

### 命令格式
```
AA 55 07 00 01 [亮度值] 0D 0A
```

### 参数说明
- 命令码：0x07
- 数据长度：1字节
- 亮度值：0-255 (0=最暗，255=最亮)

### 示例
```
AA 55 07 00 01 80 0D 0A  // 设置亮度为128 (50%)
AA 55 07 00 01 FF 0D 0A  // 设置亮度为255 (100%)
AA 55 07 00 01 00 0D 0A  // 设置亮度为0 (最暗)
```

## 特效设置命令 (0x08)

### 命令格式
```
AA 55 08 00 03 [屏幕区域][特效类型][速度] 0D 0A
```

### 参数说明
- 命令码：0x08
- 数据长度：3字节
- 屏幕区域：0x01=上半屏，0x02=下半屏，0x03=全屏
- 特效类型：见下表
- 速度：1-10 (1=最慢，10=最快)

### 特效类型对照表
| 值 | 特效名称 | 说明 |
|---|---------|------|
| 0x00 | 固定显示 | 无特效 |
| 0x01 | 左移滚动 | 文字向左滚动 |
| 0x02 | 右移滚动 | 文字向右滚动 |
| 0x03 | 闪烁特效 | 文字闪烁显示 |
| 0x04 | 呼吸特效 | 亮度渐变效果 |
|      |          |              |
|      |          |              |
| 0x07 | 向上滚动 | 文字向上滚动 |
| 0x08 | 向下滚动 | 文字向下滚动 |

### 示例
```
AA 55 08 00 03 01 01 05 0D 0A  // 上半屏左移滚动，速度5
AA 55 08 00 03 02 03 08 0D 0A  // 下半屏闪烁特效，速度8
AA 55 08 00 03 03 04 03 0D 0A  // 全屏呼吸特效，速度3
AA 55 08 00 03 01 00 00 0D 0A  // 上半屏关闭特效
```

## 注意事项
1. 所有数值均为十六进制
3. 设置新特效会自动清除该区域的其他特效
4. 亮度设置立即生效，无需重启
5. 特效速度建议范围：1-10，超出范围可能导致显示异常